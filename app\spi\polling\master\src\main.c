//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：Main.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.1
// 作    者：zhangjianzhou
// 完成时间：2024.12.29
//---------------------------------------------------------

#include "main.h"
#include "arithmetic.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "navi.h"
#include "Smi980.h"
// 宏定义临时存放位置
#define GainHeadingfromAlignment

// 全局变量定义
uint8_t   fpga_syn = 0;     // FPGA同步标志
uint32_t  TimeStamp = 0;    // 时间戳

// IMU数据缓存
float ImuData[7] = { 0 };     // 温度, 加速度(XYZ), 陀螺仪(XYZ)
int16_t data[7] = { 0 };      // 原始传感器数据

/**
 * @brief 启动诊断函数 - 检查系统启动状态和Flash配置
 * @return 0: 成功, -1: 失败
 */
static int startup_diagnostics(void)
{
    printf("=== System Startup Diagnostics ===\r\n");

    // 1. 检查复位原因
    uint32_t reset_status = HPM_PPOR->RESET_FLAG;
    printf("Reset status: 0x%08X\r\n", reset_status);
    if(reset_status & (1 << 0)) printf("  - Brownout reset detected\r\n");
    if(reset_status & (1 << 4)) printf("  - Debug reset detected\r\n");
    if(reset_status & (1 << 16)) printf("  - Watchdog0 reset detected\r\n");
    if(reset_status & (1 << 17)) printf("  - Watchdog1 reset detected\r\n");
    if(reset_status & (1 << 24)) printf("  - PMIC watchdog reset detected\r\n");
    if(reset_status & (1 << 31)) printf("  - Software reset detected\r\n");

    // 清除复位标志
    HPM_PPOR->RESET_FLAG = reset_status;

    // 2. 诊断Flash配置选项位置
    norflash_diagnose_config_locations();

    // 3. 验证Flash配置选项
    int cfg_status = norflash_verify_config_option();
    if(cfg_status == 0) {
        printf("ERROR: Flash config option corrupted!\r\n");
        printf("Attempting automatic recovery...\r\n");

        if(norflash_restore_config_option() == 0) {
            printf("Flash config option restored successfully\r\n");
            printf("System will reset to apply new configuration...\r\n");
            board_delay_ms(100);
            Drv_SystemReset();
            return -1; // 不会执行到这里
        } else {
            printf("CRITICAL: Failed to restore Flash config option!\r\n");
            return -1;
        }
    } else if(cfg_status == -1) {
        printf("ERROR: Failed to read Flash config option!\r\n");
        return -1;
    } else {
        printf("Flash config option: OK\r\n");
    }

    // 4. Flash基本功能测试
    uint32_t flash_size = norflash_get_chip_size();
    printf("Flash size: %d bytes (%.1f MB)\r\n", flash_size, flash_size / 1024.0f / 1024.0f);

    // 5. Flash读写测试
    uint32_t test_data = 0x12345678;
    uint32_t read_data = 0;
    uint32_t test_addr = 0x50000; // 使用安全的测试地址

    if(norflash_write(test_addr, &test_data, 4) == 0) {
        if(norflash_read(test_addr, &read_data, 4) == 0) {
            if(read_data == test_data) {
                printf("Flash read/write test: PASSED\r\n");
            } else {
                printf("Flash read/write test: FAILED (data mismatch)\r\n");
                printf("  Expected: 0x%08X, Got: 0x%08X\r\n", test_data, read_data);
                return -1;
            }
        } else {
            printf("Flash read test: FAILED\r\n");
            return -1;
        }
    } else {
        printf("Flash write test: FAILED\r\n");
        return -1;
    }

    printf("=== Startup Diagnostics Completed ===\r\n");
    return 0;
}

/**
 * @brief 根据任务类型管理UART数据输出
 * @param Task 任务类型枚举
 */
static void TaskMange(WorkTask_e Task)
{
    // 计算每帧可传输的字节数
    uint16_t ByteCount = (uint16_t)((stSetPara.Setbaud*100)/(10*stSetPara.Setfre));

    // 根据任务类型选择输出模式
    switch(Task)
    {
        case Original_Task: // 原始数据输出 37个字节
            Smi240UartSend(ImuData);
            break;

        case Combination_Task: // 组合惯导输出
            if(ByteCount >= 65)
            {
                CombinationUartSend(ImuData);
            }
            else
            {
                CombinationUartSend22B(ImuData); // 使用较小的数据包
            }
            break;

        case Pure_Task: // 纯惯导输出
            if(ByteCount >= 67)
            {
                PureUartSend(&InavOutData);
            }
            else
            {
                PureUartSend36B(&InavOutData); // 使用较小的数据包
            }
            break;

          default:
			
          break;
    }
}

/**
 * @brief 根据任务类型管理SPI2数据输出
 * @param Task 任务类型枚举
 */
static void Spi2Task(WorkTask_e Task)
{
    // 根据任务类型选择SPI2输出模式
    switch(Task)
    {
          case Original_Task: //原始数据输出 33个字节
              Smi240Spi2Send(ImuData);
          break;
          case Combination_Task://组合惯导输出
              CombinationSpi2Send(ImuData);
          break;
          case Pure_Task://纯惯导输出
              PureSpi2Send(ImuData);
          break;

          default:
			
          break;
    }
}

extern Smi240_Data stSmi240Data;

/**
 * @brief 用户任务处理函数，处理传感器数据并执行算法
 */
static void UserTask(void)
{   
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;

    // 读取SMI240传感器数据
    Smi980_ReadData(ImuData);
    GetSmi240Data(ImuData);

    // 陀螺仪温度补偿处理
    g_Compen.GyroRaw[0] = stSmi240Data.gyro_x;
    g_Compen.GyroRaw[1] = stSmi240Data.gyro_y;
    g_Compen.GyroRaw[2] = stSmi240Data.gyro_z;

    // 所有轴共用一个温度计
    const float gyroTemp = stSmi240Data.temp_UNO;
    g_Compen.GyroTemp[0] = gyroTemp;
    g_Compen.GyroTemp[1] = gyroTemp;
    g_Compen.GyroTemp[2] = gyroTemp;

    // 执行陀螺仪温度补偿计算
    GyroCompenCompute(&g_Compen,
                     &g_CmdNormalTempCompenData.GyroNormalTempData,
                     &g_CmdFullTempCompenData.GyroFullTempData,
                     &g_GyroANNCompen);

    // 加速度计温度补偿处理
    g_Compen.AccRaw[0] = stSmi240Data.accel_x;
    g_Compen.AccRaw[1] = stSmi240Data.accel_y;
    g_Compen.AccRaw[2] = stSmi240Data.accel_z;

    // 所有轴使用同一温度值
    for(int i = 0; i < 3; i++) {
        g_Compen.AccTemp[i] = gyroTemp;
    }

    // 执行加速度计温度补偿计算
    AccCompenCompute(&g_Compen,
                    &g_CmdNormalTempCompenData.AccNormalTempData,
                    &g_CmdFullTempCompenData.AccFullTempData,
                    &g_AccANNCompen);

    // 将传感器数据传入算法处理
    Smi240DataToAlgorithm(&g_Compen);

    // 执行导航算法 (200Hz)
    AlgorithmDo();

    // 根据设定的输出频率输出数据
    if(tCnt >= freq) {
        tCnt = 0;
        TaskMange(stSetPara.SetDataOutType);

        TimeStamp += (1000/stSetPara.Setfre);//更新时间戳
    }
   
}

/**
 * @brief 主函数
 * @return 程序执行状态码
 */
int main(void)
{
    // 添加启动延时，确保电源稳定
    for(volatile int i = 0; i < 2000000; i++); // 增加延时时间

    // 初始化板级硬件
    board_init();

    // 添加硬件复位后的稳定延时，确保Flash接口稳定
    for(volatile int i = 0; i < 1000000; i++); // 增加延时时间

    // 执行启动诊断
    if(startup_diagnostics() != 0) {
        printf("CRITICAL: Startup diagnostics failed!\r\n");
        printf("System may not function properly.\r\n");
        // 可以选择继续运行或停止
        // while(1); // 如果要停止系统
    }

    // 初始化Flash存储 - 提前初始化，确保Flash配置正确
    norflash_init();

    // 验证Flash是否正常工作
    uint32_t flash_id = 0;
    if(norflash_read(0, &flash_id, 4) != 0) {
        printf("Flash read test failed, reinitializing...\r\n");
        // Flash初始化失败，重新初始化
        norflash_init();
    }

    // 从Flash读取参数配置
    ReadParaFromFlash();

    // 验证参数是否正确读取
    printf("Flash parameter Flag: %d\r\n", stSetPara.Flag);

    // 初始化SPI接口
    SpiInitMaster();
    SpiInitSlave();

    // 初始化SMI240传感器
    Smi980_Init();

    // 初始化UART和定时器
    UartIrqInit();
    Timer_Init();
    printf("Smi240 App v1.1\r\n");

    Sys_Init(); // 系统初始化

    Bind_Init(&g_InitBind);

    // 初始化补偿参数
    Gyro_Compen_Para_Init(&g_CmdNormalTempCompenData.GyroNormalTempData,&g_CmdFullTempCompenData.GyroFullTempData);
    Acc_Compen_Para_Init(&g_CmdNormalTempCompenData.AccNormalTempData,&g_CmdFullTempCompenData.AccFullTempData);
    // 初始化神经网络补偿
    ANNCompen_Init();

    // 主循环
    while(1)
    {
        // FPGA同步触发的任务处理
        if(fpga_syn)
        {
            fpga_syn = 0;
            UserTask();
          /*  printf("ACC_x: %f\n", stSmi240Data.accel_x);
            printf("ACC_y: %f\n", stSmi240Data.accel_y);
            printf("ACC_z: %f\n", stSmi240Data.accel_z);
            printf("gyro_x: %f\n", stSmi240Data.gyro_x);
            printf("gyro_y: %f\n", stSmi240Data.gyro_y);
            printf("gyro_z: %f\n",stSmi240Data.gyro_z);
            printf("temp: %f\n", stSmi240Data.temp_UNO);*/
        }

        analysisRxdata();

        Spi2Task(stSetPara.SetDataOutType);
    }

    return 0;
}

