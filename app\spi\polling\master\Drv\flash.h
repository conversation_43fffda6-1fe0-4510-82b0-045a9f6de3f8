//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：flash.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.21
//---------------------------------------------------------


#ifndef _FLASH_H
#define _FLASH_H

#include <stdio.h>
#include "board.h"
#include "hpm_debug_console.h"
#include "hpm_l1c_drv.h"
#include "hpm_romapi.h"
#include "hpm_clock_drv.h"


//#define APP_UPDATE_ADDRESS		0x32000	//0x80001000	

	

//初始化FLASH
int norflash_init(void);
//重置XPI接口
int norflash_reset_xpi_interface(void);
//扇区擦除
int norflash_erase_sector(uint32_t offset);

int norflash_write(uint32_t offset, const void *buf, uint32_t size_bytes);

int norflash_read(uint32_t offset, void *buf, uint32_t size_bytes);

int norflash_read_mem(uint32_t offset, void *buf, uint32_t size_bytes);

int norflash_erase_chip(void);
int norflash_erase_block(uint32_t offset);

uint32_t norflash_get_chip_size(void);
uint32_t norflash_get_block_size(void);
uint8_t norflash_get_erase_value(void);

// Flash配置选项相关函数
int norflash_verify_config_option(void);
int norflash_restore_config_option(void);
int norflash_diagnose_config_locations(void);
int norflash_sync_config_options(void);
int norflash_check_config_protection(void);

// Flash配置选项保护函数
int norflash_write_protected(uint32_t offset, const void *buf, uint32_t size_bytes);
int norflash_erase_sector_protected(uint32_t offset);

//FLASH测试程序
void FlashTest(void);
#endif