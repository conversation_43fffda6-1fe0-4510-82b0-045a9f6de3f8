# Flash配置选项修复测试指南

## 测试目的
验证Flash配置选项地址修复是否解决了设备重启后无法正常工作的问题。

## 测试前准备

### 1. 备份当前固件
```bash
# 使用调试器读取当前Flash内容并保存
```

### 2. 编译新固件
确保使用修复后的代码编译新的固件。

## 测试步骤

### 第一阶段：诊断测试

1. **烧录新固件**
   - 烧录包含修复的新固件
   - 连接串口监控输出

2. **观察启动诊断信息**
   查看串口输出，应该看到类似以下信息：
   ```
   === System Startup Diagnostics ===
   Reset status: 0x00000001
     - Brownout reset detected
   
   === Flash Config Option Location Diagnosis ===
   Checking config at NEW location (0x8000D000):
     [0xfcf90002, 0x00000006, 0x00001000, 0x00000000]
   Checking config at OLD location (0x80000400):
     [0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF]
   === End Diagnosis ===
   
   Reading Flash config option from offset 0xD000
   Current config option: [0xfcf90002, 0x00000006, 0x00001000, 0x00000000]
   Flash config option verification: PASSED
   ```

3. **验证正常工作**
   - 检查电流是否为 0.08A
   - 验证数据输出是否正常

### 第二阶段：重启测试

1. **断电重启测试**
   - 断开设备电源
   - 等待5秒
   - 重新上电
   - 观察串口输出和设备状态

2. **预期结果**
   - 设备应该正常启动
   - 电流应该保持在 0.08A
   - 数据输出应该正常
   - 串口应该显示配置选项验证通过

### 第三阶段：多次重启测试

1. **连续重启测试**
   - 进行10次断电重启
   - 每次重启后检查设备状态

2. **长时间稳定性测试**
   - 设备连续运行24小时
   - 期间进行5次随机重启
   - 记录每次重启后的状态

## 故障排除

### 如果设备仍然无法正常重启

1. **检查配置选项位置**
   查看诊断输出，确认配置选项在哪个位置：
   - 如果在 `0x8000D000` 有正确配置，但在 `0x80000400` 是空的，说明修复正确
   - 如果两个位置都是空的，可能需要手动恢复配置选项

2. **手动恢复配置选项**
   如果自动恢复失败，可以通过调试器手动写入：
   ```
   地址: 0x8000D000
   数据: 0xfcf90002, 0x00000006, 0x00001000, 0x00000000
   ```

3. **检查链接器配置**
   确认使用的链接器文件是：
   `app/spi/polling/master/hpm5321_flash_xip_debug/segger_embedded_studio/flash_xip.icf`

## 测试记录表

| 测试项目 | 预期结果 | 实际结果 | 通过/失败 | 备注 |
|---------|---------|---------|----------|------|
| 首次烧录启动 | 正常启动，0.08A | | | |
| 配置选项诊断 | 显示正确位置配置 | | | |
| 第1次重启 | 正常启动，0.08A | | | |
| 第2次重启 | 正常启动，0.08A | | | |
| 第3次重启 | 正常启动，0.08A | | | |
| ... | ... | | | |
| 第10次重启 | 正常启动，0.08A | | | |

## 成功标准

测试通过的标准：
1. 设备首次烧录后正常工作
2. 连续10次重启后都能正常工作
3. 每次重启后电流保持在 0.08A
4. 数据输出正常
5. 串口显示配置选项验证通过

## 注意事项

1. **安全第一**：在测试过程中确保设备在安全环境中运行
2. **数据备份**：测试前备份重要数据
3. **记录详细**：记录每次测试的详细结果
4. **问题报告**：如果发现问题，立即记录串口输出和设备状态
