Output/Debug/Obj/spi_polling_master_example\ -\ hpm5321/ZUPT.o: \
 D:\test\Smi240\Smi980\app\spi\polling\master\Arithmetic\ZUPT.c \
 D:\test\Smi240\Smi980\app\spi\polling\master\Arithmetic\ZUPT.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/stdint.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/__SEGGER_RTL.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/__SEGGER_RTL_ConfDefaults.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/__SEGGER_RTL_Conf.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/__SEGGER_RTL_RISCV_Conf.h \
 ../../src/EXTERNGLOBALDATA.h ../../src/DATASTRUCT.h ../../src/CONST.h \
 ../../src/TYPEDEFINE.h \
 D:\test\Smi240\Smi980\app\spi\polling\master\Arithmetic\arithmetic.h \
 D:\test\Smi240\Smi980\app\spi\polling\master\Arithmetic\align.h \
 ../../src/DATASTRUCT.h \
 D:\test\Smi240\Smi980\app\spi\polling\master\Arithmetic\navi.h \
 ../../src/FUNCTION.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/stdio.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/stdlib.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/math.h \
 D:/softwawe/SEGGER\ Embedded\ Studio\ 8.12a/include/__SEGGER_RTL_FP.h
