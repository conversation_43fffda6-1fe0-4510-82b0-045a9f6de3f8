//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：flash.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.21
//---------------------------------------------------------

#include "flash.h"


static xpi_nor_config_t s_xpi_nor_config;
int norflash_init(void)
{
    printf("Flash init: Setting up config options...\r\n");

    xpi_nor_config_option_t option;
    option.header.U = BOARD_APP_XPI_NOR_CFG_OPT_HDR;
    option.option0.U = BOARD_APP_XPI_NOR_CFG_OPT_OPT0;
    option.option1.U = BOARD_APP_XPI_NOR_CFG_OPT_OPT1;

    printf("Flash init: Config options - HDR:0x%08X, OPT0:0x%08X, OPT1:0x%08X\r\n",
           option.header.U, option.option0.U, option.option1.U);

    printf("Flash init: Calling rom_xpi_nor_auto_config...\r\n");
    hpm_stat_t status = rom_xpi_nor_auto_config(BOARD_APP_XPI_NOR_XPI_BASE, &s_xpi_nor_config, &option);

    if (status != status_success) {
        printf("ERROR: rom_xpi_nor_auto_config failed with status: 0x%08X\r\n", status);
        printf("Flash init: XPI base address: 0x%08X\r\n", (uint32_t)BOARD_APP_XPI_NOR_XPI_BASE);

        // 不要进入死循环，返回错误让上层处理
        return -1;
    }

    printf("Flash init: rom_xpi_nor_auto_config succeeded\r\n");

    // 验证Flash配置
    uint32_t flash_size = 0;
    status = rom_xpi_nor_get_property(BOARD_APP_XPI_NOR_XPI_BASE, &s_xpi_nor_config,
                                     xpi_nor_property_total_size, &flash_size);
    if (status == status_success) {
        printf("Flash init: Detected flash size: %d bytes\r\n", flash_size);
    } else {
        printf("Flash init: WARNING - Could not get flash size\r\n");
    }

    return 0;
}

// XPI接口重置函数
int norflash_reset_xpi_interface(void)
{
    printf("Resetting XPI interface...\r\n");

    // 获取XPI基地址
    XPI_Type *xpi_base = BOARD_APP_XPI_NOR_XPI_BASE;

    // 执行软件重置
    if (ROM_API_TABLE_ROOT && ROM_API_TABLE_ROOT->xpi_driver_if &&
        ROM_API_TABLE_ROOT->xpi_driver_if->software_reset) {
        ROM_API_TABLE_ROOT->xpi_driver_if->software_reset(xpi_base);
        printf("XPI software reset executed\r\n");
    } else {
        printf("WARNING: XPI software reset function not available\r\n");
    }

    // 添加延时让接口稳定
    for(volatile int i = 0; i < 1000000; i++);

    // 执行fence指令确保所有操作完成
    __asm volatile("fence.i" ::: "memory");

    printf("XPI interface reset complete\r\n");
    return 0;
}

int norflash_read(uint32_t offset, void *buf, uint32_t size_bytes)
{
    hpm_stat_t status = rom_xpi_nor_read(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto,
                        &s_xpi_nor_config, (uint32_t *)buf, offset, size_bytes);
    if (status != status_success) {
        return -1;
    }

    return 0;
}

int norflash_read_mem(uint32_t offset, void *buf, uint32_t size_bytes)
{
    uint32_t flash_addr = 0x80000000 + offset;
    uint32_t aligned_start = HPM_L1C_CACHELINE_ALIGN_DOWN(flash_addr);
    uint32_t aligned_end = HPM_L1C_CACHELINE_ALIGN_UP(flash_addr + size_bytes);
    uint32_t aligned_size = aligned_end - aligned_start;

    l1c_dc_invalidate(aligned_start, aligned_size);

    memcpy(buf, (void *)flash_addr, size_bytes);
    return 0;
}

int norflash_write(uint32_t offset, const void *buf, uint32_t size_bytes)
{
    hpm_stat_t status = rom_xpi_nor_program(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto,
                                 &s_xpi_nor_config, (const uint32_t *)buf, offset, size_bytes);
    if (status != status_success) {
        return -1;
    }
    return 0;
}

int norflash_erase_chip(void)
{
    hpm_stat_t status = rom_xpi_nor_erase_chip(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto, &s_xpi_nor_config);
    if (status != status_success) {
        return -1;
    }
    return 0;
}

int norflash_erase_block(uint32_t offset)
{
    hpm_stat_t status = rom_xpi_nor_erase_block(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto, &s_xpi_nor_config, offset);
    if (status != status_success) {
        return -1;
    }
    return 0;
}

//扇区擦除
int norflash_erase_sector(uint32_t offset)
{
    hpm_stat_t status = rom_xpi_nor_erase_sector(BOARD_APP_XPI_NOR_XPI_BASE, xpi_xfer_channel_auto, &s_xpi_nor_config, offset);
    if (status != status_success) {
        return -1;
    }
    return 0;
}


uint32_t norflash_get_chip_size(void)
{
    uint32_t flash_size;
    rom_xpi_nor_get_property(BOARD_APP_XPI_NOR_XPI_BASE, &s_xpi_nor_config, 
            xpi_nor_property_total_size, &flash_size);
    return flash_size;
}

uint32_t norflash_get_block_size(void)
{
    uint32_t block_size;
    rom_xpi_nor_get_property(BOARD_APP_XPI_NOR_XPI_BASE, &s_xpi_nor_config, 
            xpi_nor_property_block_size, &block_size);
    return block_size;
}

uint8_t norflash_get_erase_value(void)
{
    return 0xffu;
}

// Flash配置选项地址定义
// 重要发现：芯片启动时实际在 0x80000400 查找配置选项，而不是链接器配置的 0x8000D000
#define FLASH_CONFIG_OPTION_OFFSET  0x400   // 芯片实际查找的地址 (0x80000400)
#define FLASH_CONFIG_OPTION_OFFSET_LINKER  0xD000  // 链接器配置的地址 (0x8000D000)

// 诊断函数：检查两个可能的Flash配置选项位置
int norflash_diagnose_config_locations(void)
{
    uint32_t config_chip[4], config_linker[4];

    printf("=== Flash Config Option Location Diagnosis ===\r\n");

    // 检查芯片实际查找的地址 (0x80000400)
    printf("Checking config at CHIP location (0x8000%X):\r\n", FLASH_CONFIG_OPTION_OFFSET);
    if(norflash_read_mem(FLASH_CONFIG_OPTION_OFFSET, config_chip, sizeof(config_chip)) == 0) {
        printf("  [0x%08X, 0x%08X, 0x%08X, 0x%08X]\r\n",
               config_chip[0], config_chip[1], config_chip[2], config_chip[3]);
    } else {
        printf("  Failed to read from chip location\r\n");
    }

    // 检查链接器配置的地址 (0x8000D000)
    printf("Checking config at LINKER location (0x8000%X):\r\n", FLASH_CONFIG_OPTION_OFFSET_LINKER);
    if(norflash_read_mem(FLASH_CONFIG_OPTION_OFFSET_LINKER, config_linker, sizeof(config_linker)) == 0) {
        printf("  [0x%08X, 0x%08X, 0x%08X, 0x%08X]\r\n",
               config_linker[0], config_linker[1], config_linker[2], config_linker[3]);
    } else {
        printf("  Failed to read from linker location\r\n");
    }

    printf("=== End Diagnosis ===\r\n");
    return 0;
}

// 同步Flash配置选项：确保芯片查找位置和链接器位置都有正确的配置
int norflash_sync_config_options(void)
{
    uint32_t expected_option[4] = {0xfcf90002, 0x00000006, 0x1000, 0x0};
    uint32_t config_chip[4], config_linker[4];
    bool chip_valid = false, linker_valid = false;

    printf("=== Synchronizing Flash Config Options ===\r\n");

    // 检查芯片位置的配置
    if(norflash_read_mem(FLASH_CONFIG_OPTION_OFFSET, config_chip, sizeof(config_chip)) == 0) {
        chip_valid = true;
        for(int i = 0; i < 4; i++) {
            if(config_chip[i] != expected_option[i]) {
                chip_valid = false;
                break;
            }
        }
    }

    // 检查链接器位置的配置
    if(norflash_read_mem(FLASH_CONFIG_OPTION_OFFSET_LINKER, config_linker, sizeof(config_linker)) == 0) {
        linker_valid = true;
        for(int i = 0; i < 4; i++) {
            if(config_linker[i] != expected_option[i]) {
                linker_valid = false;
                break;
            }
        }
    }

    printf("Chip location (0x8000%X) valid: %s\r\n", FLASH_CONFIG_OPTION_OFFSET, chip_valid ? "YES" : "NO");
    printf("Linker location (0x8000%X) valid: %s\r\n", FLASH_CONFIG_OPTION_OFFSET_LINKER, linker_valid ? "YES" : "NO");

    // 如果芯片位置无效但链接器位置有效，复制配置
    if(!chip_valid && linker_valid) {
        printf("Copying config from linker location to chip location...\r\n");
        if(norflash_erase_sector(FLASH_CONFIG_OPTION_OFFSET) == 0) {
            if(norflash_write(FLASH_CONFIG_OPTION_OFFSET, config_linker, sizeof(config_linker)) == 0) {
                printf("Config copied successfully\r\n");
                return 1; // 需要重启
            } else {
                printf("Failed to write config to chip location\r\n");
            }
        } else {
            printf("Failed to erase chip location\r\n");
        }
    }
    // 如果链接器位置无效但芯片位置有效，复制配置
    else if(chip_valid && !linker_valid) {
        printf("Copying config from chip location to linker location...\r\n");
        if(norflash_erase_sector(FLASH_CONFIG_OPTION_OFFSET_LINKER) == 0) {
            if(norflash_write(FLASH_CONFIG_OPTION_OFFSET_LINKER, config_chip, sizeof(config_chip)) == 0) {
                printf("Config copied successfully\r\n");
            } else {
                printf("Failed to write config to linker location\r\n");
            }
        } else {
            printf("Failed to erase linker location\r\n");
        }
    }
    // 如果两个位置都无效，写入正确的配置
    else if(!chip_valid && !linker_valid) {
        printf("Both locations invalid, writing correct config...\r\n");

        // 写入芯片位置
        if(norflash_erase_sector(FLASH_CONFIG_OPTION_OFFSET) == 0) {
            if(norflash_write(FLASH_CONFIG_OPTION_OFFSET, expected_option, sizeof(expected_option)) == 0) {
                printf("Config written to chip location\r\n");
            }
        }

        // 写入链接器位置
        if(norflash_erase_sector(FLASH_CONFIG_OPTION_OFFSET_LINKER) == 0) {
            if(norflash_write(FLASH_CONFIG_OPTION_OFFSET_LINKER, expected_option, sizeof(expected_option)) == 0) {
                printf("Config written to linker location\r\n");
            }
        }

        return 1; // 需要重启
    }

    printf("=== Sync Complete ===\r\n");
    return 0; // 不需要重启
}

// 检查Flash配置选项是否被意外覆盖
int norflash_check_config_protection(void)
{
    uint32_t config_option[4];
    uint32_t expected_option[4] = {0xfcf90002, 0x00000006, 0x1000, 0x0};

    // 读取当前配置选项
    if(norflash_read_mem(FLASH_CONFIG_OPTION_OFFSET, config_option, sizeof(config_option)) != 0) {
        printf("WARNING: Cannot read Flash config option for protection check\r\n");
        return -1;
    }

    // 检查是否被覆盖
    for(int i = 0; i < 4; i++) {
        if(config_option[i] != expected_option[i]) {
            printf("CRITICAL: Flash config option has been corrupted during runtime!\r\n");
            printf("  Option[%d]: expected 0x%08X, got 0x%08X\r\n",
                   i, expected_option[i], config_option[i]);

            // 立即恢复
            printf("Attempting immediate recovery...\r\n");
            if(norflash_restore_config_option() == 0) {
                printf("Config option recovered, system will reset\r\n");
                board_delay_ms(100);
                Drv_SystemReset();
            }
            return -1;
        }
    }

    return 0; // 配置选项正常
}

// 添加Flash配置选项保护写入函数
int norflash_write_protected(uint32_t offset, const void *buf, uint32_t size_bytes)
{
    // 检查是否会覆盖Flash配置选项区域
    if((offset <= FLASH_CONFIG_OPTION_OFFSET + 16) &&
       (offset + size_bytes > FLASH_CONFIG_OPTION_OFFSET)) {
        printf("WARNING: Write operation would overwrite Flash config option!\r\n");
        printf("  Write range: 0x%X - 0x%X\r\n", offset, offset + size_bytes);
        printf("  Config range: 0x%X - 0x%X\r\n", FLASH_CONFIG_OPTION_OFFSET, FLASH_CONFIG_OPTION_OFFSET + 16);

        // 备份当前配置选项
        uint32_t config_backup[4];
        if(norflash_read_mem(FLASH_CONFIG_OPTION_OFFSET, config_backup, sizeof(config_backup)) == 0) {
            printf("Config option backed up successfully\r\n");

            // 执行写入操作
            int result = norflash_write(offset, buf, size_bytes);

            // 恢复配置选项
            if(norflash_write(FLASH_CONFIG_OPTION_OFFSET, config_backup, sizeof(config_backup)) == 0) {
                printf("Config option restored after write\r\n");
            } else {
                printf("CRITICAL: Failed to restore config option after write!\r\n");
            }

            return result;
        } else {
            printf("CRITICAL: Cannot backup config option, write aborted!\r\n");
            return -1;
        }
    }

    // 正常写入
    return norflash_write(offset, buf, size_bytes);
}

// 添加Flash配置选项保护擦除函数
int norflash_erase_sector_protected(uint32_t offset)
{
    uint32_t sector_size = 0x1000; // 4KB扇区大小
    uint32_t sector_start = (offset / sector_size) * sector_size;
    uint32_t sector_end = sector_start + sector_size;

    // 检查是否会擦除Flash配置选项区域
    if((sector_start <= FLASH_CONFIG_OPTION_OFFSET) &&
       (sector_end > FLASH_CONFIG_OPTION_OFFSET)) {
        printf("WARNING: Erase operation would destroy Flash config option!\r\n");
        printf("  Erase sector: 0x%X - 0x%X\r\n", sector_start, sector_end);
        printf("  Config at: 0x%X\r\n", FLASH_CONFIG_OPTION_OFFSET);

        // 备份当前配置选项
        uint32_t config_backup[4];
        if(norflash_read_mem(FLASH_CONFIG_OPTION_OFFSET, config_backup, sizeof(config_backup)) == 0) {
            printf("Config option backed up successfully\r\n");

            // 执行擦除操作
            int result = norflash_erase_sector(offset);

            // 恢复配置选项
            if(norflash_write(FLASH_CONFIG_OPTION_OFFSET, config_backup, sizeof(config_backup)) == 0) {
                printf("Config option restored after erase\r\n");
            } else {
                printf("CRITICAL: Failed to restore config option after erase!\r\n");
            }

            return result;
        } else {
            printf("CRITICAL: Cannot backup config option, erase aborted!\r\n");
            return -1;
        }
    }

    // 正常擦除
    return norflash_erase_sector(offset);
}

// Flash配置选项验证和恢复函数
int norflash_verify_config_option(void)
{
    uint32_t nor_cfg_option[4];
    uint32_t expected_option[4] = {0xfcf90002, 0x00000006, 0x1000, 0x0};

    printf("Reading Flash config option from offset 0x%X\r\n", FLASH_CONFIG_OPTION_OFFSET);

    // 读取Flash配置选项区域 - 使用正确的地址
    if(norflash_read_mem(FLASH_CONFIG_OPTION_OFFSET, nor_cfg_option, sizeof(nor_cfg_option)) != 0) {
        printf("Failed to read Flash config option\r\n");
        return -1; // 读取失败
    }

    printf("Current config option: [0x%08X, 0x%08X, 0x%08X, 0x%08X]\r\n",
           nor_cfg_option[0], nor_cfg_option[1], nor_cfg_option[2], nor_cfg_option[3]);

    // 验证配置选项是否正确
    for(int i = 0; i < 4; i++) {
        if(nor_cfg_option[i] != expected_option[i]) {
            printf("Config option[%d]: expected 0x%08X, got 0x%08X\r\n",
                   i, expected_option[i], nor_cfg_option[i]);
            return 0; // 配置选项不匹配
        }
    }

    printf("Flash config option verification: PASSED\r\n");
    return 1; // 配置选项正确
}

// 恢复Flash配置选项
int norflash_restore_config_option(void)
{
    uint32_t expected_option[4] = {0xfcf90002, 0x00000006, 0x1000, 0x0};

    printf("Restoring Flash config option at offset 0x%X...\r\n", FLASH_CONFIG_OPTION_OFFSET);

    // 擦除配置选项扇区 - 使用正确的地址
    if(norflash_erase_sector(FLASH_CONFIG_OPTION_OFFSET) != 0) {
        printf("Failed to erase config option sector\r\n");
        return -1;
    }

    printf("Config option sector erased successfully\r\n");

    // 写入正确的配置选项 - 使用正确的地址
    if(norflash_write(FLASH_CONFIG_OPTION_OFFSET, expected_option, sizeof(expected_option)) != 0) {
        printf("Failed to write config option\r\n");
        return -1;
    }

    printf("Config option written successfully\r\n");

    // 验证写入是否成功
    if(norflash_verify_config_option() == 1) {
        printf("Flash config option restored successfully\r\n");
        return 0;
    } else {
        printf("Flash config option restore failed\r\n");
        return -1;
    }
}

////擦除扇区
//void  Drv_FlashErase(uint32_t address)
//{
//    uint8_t Level = disable_global_irq(CSR_MSTATUS_MIE_MASK);
//    norflash_erase_sector(address);
//    //enable_global_irq(CSR_MSTATUS_MIE_MASK);
//    restore_global_irq(Level);
    
//}

////写入FLASH
//void Drv_FlashWrite(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen)
//{
//    uint8_t Level = disable_global_irq(CSR_MSTATUS_MIE_MASK);
//    norflash_write(uiAddress, pucBuff, uiLen);
//    //enable_global_irq(CSR_MSTATUS_MIE_MASK);
//    restore_global_irq(Level);
//}

////读取FLASH
//void Drv_FlashRead(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen)
//{
//  norflash_read(uiAddress, pucBuff, uiLen);
//}

////测试FLASH
//uint8_t WriteTestData[20] = {0x055,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,0x06,0x04,0x55,0x15};
//uint8_t WriteTestData1[20] = {0xAA,0x0B,0x0C,0x0D,0x0E,0x0F,0x43,0x48,0x69,0x7A,0xCB,0xAC,0x1D,0x6E,0x8F,0x06,0x04,0x35,0x97,0xAA};

//uint8_t ReadTestData[10];
//uint8_t AppReadTestData[10];
//void FlashTest(void)
//{
//    uint8_t temp[5];
//    Drv_FlashErase(APP_UPDATE_ADDRESS);
//    //模拟写入到备份区
//    memset(ReadTestData,0,sizeof(ReadTestData));
//    Drv_FlashWrite(WriteTestData,APP_UPDATE_ADDRESS,20);
//    Drv_FlashRead(ReadTestData,APP_UPDATE_ADDRESS,10);

//    memset(ReadTestData,0,sizeof(ReadTestData));
//    Drv_FlashWrite(WriteTestData1,APP_UPDATE_ADDRESS+20,20);
//    Drv_FlashRead(ReadTestData+5,APP_UPDATE_ADDRESS+20,5);


//    //模拟Bootloader读取备份区数据，并写入运行区
//    memset(ReadTestData,0,sizeof(ReadTestData));
//    Drv_FlashRead(ReadTestData,APP_UPDATE_ADDRESS,10);

//}
