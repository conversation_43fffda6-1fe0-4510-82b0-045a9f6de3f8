***********************************************************************************************
***                                                                                         ***
***                                    LINK INFORMATION                                     ***
***                                                                                         ***
***********************************************************************************************

Linker version:

  SEGGER RISC-V Linker 4.38.11 compiled Jun 11 2024 14:00:05
  Copyright (c) 2017-2022 SEGGER Microcontroller GmbH    www.segger.com


***********************************************************************************************
***                                                                                         ***
***                                     MODULE SUMMARY                                      ***
***                                                                                         ***
***********************************************************************************************

Memory use by input file:

  Object File                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  align.o                                             2 310          40                        
  AnnTempCompen.o                                    47 674                                    
  arithmetic.o                                        4 282          32                     312
  board.c.o                                           2 392         956                       4
  compen.o                                           20 590          24                        
  FirmwareUpdateFile.o                                  186                                    
  flash.o                                             1 062         852                     256
  hpm_bootheader.c.o                                                144                        
  hpm_clock_drv.c.o                                   1 776         140                       4
  hpm_debug_console.c.o                                 252                       4           8
  hpm_gptmr_drv.c.o                                     420                                    
  hpm_l1c_drv.c.o                                       390         187                        
  hpm_pcfg_drv.c.o                                       72                                    
  hpm_pllctlv2_drv.c.o                                  700          24                        
  hpm_spi_drv.c.o                                     2 476                                    
  hpm_sysctl_drv.c.o                                    750                                    
  hpm_uart_drv.c.o                                    1 384          24                        
  main.o                                              1 784         968                  37 227
  matvecmath.o                                        2 536           8                        
  navi.o                                              6 626         152                        
  pinmux.c.o                                            330                                    
  protocol.o                                          6 664          32                      28
  reset.c.o                                             198                                    
  SetParaBao.o                                       20 426         332           5       5 957
  Smi980.o                                              488          44                        
  spi.o                                                 790       1 051                      24
  startup.s.o                                           108         292                        
  system.c.o                                            130                                    
  Timer.o                                               536                                    
  trap.c.o                                              384          64                        
  uart_dma.o                                            414                       1           1
  Uart_Irq.o                                          1 856          26           8       9 226
  ZUPT.o                                              2 286         808                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (33 objects)                             132 272       6 200          18      53 047
  ---------------------------------------------  ----------  ----------  ----------  ----------
  heapops_basic_rv32imac_balanced.a                      22                                   4
  libc_rv32imac_balanced.a                           14 480       2 486                      24
  mbops_timeops_rv32imac_balanced.a                     230         549          20           4
  SEGGER_RV32_crtinit_rv32imac_balanced.a                68                                    
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (4 archives)                              14 800       3 035          20          32
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Linker created (shared data, fills, blocks):                    1 944                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            147 072      11 179          38      85 847
  =============================================  ==========  ==========  ==========  ==========

Memory use by archive member:

  Archive member                                    RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
                                                      3 220         248                        
  convops.o (libc_rv32imac_balanced.a)                  134                                    
  execops.o (libc_rv32imac_balanced.a)                  262          30                      24
  fileops.o (libc_rv32imac_balanced.a)                  172                                    
  floatasmops_rv.o (libc_rv32imac_balanced.a)         3 888         256                        
  floatops.o (libc_rv32imac_balanced.a)               3 532         712                        
  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
                                                         22                                   4
  intasmops_rv.o (libc_rv32imac_balanced.a)              38                                    
  intops.o (libc_rv32imac_balanced.a)                 2 150       1 024                        
  mbops.o (mbops_timeops_rv32imac_balanced.a)           230         549          20           4
  prinops.o (libc_rv32imac_balanced.a)                  500         192                        
  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
                                                         68                                    
  strasmops_rv.o (libc_rv32imac_balanced.a)             342                                    
  strops.o (libc_rv32imac_balanced.a)                   152                                    
  utilops.o (libc_rv32imac_balanced.a)                   90          24                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (15 members from 4 archives)              14 800       3 035          20          32
  Objects (33 files)                                132 272       6 200          18      53 047
  Linker created (shared data, fills, blocks):                    1 944                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            147 072      11 179          38      85 847
  =============================================  ==========  ==========  ==========  ==========

Memory use by linker:

  Description                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Initialization table                                            1 820                        
  Memory for block 'heap'                                                                16 384
  Memory for block 'stack'                                                               16 384
  Merged section data (32-bit)                                       28                        
  Merged section data (64-bit)                                       96                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (linker created):                                      1 944                  32 768
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Objects (33 files)                                132 272       6 200          18      53 047
  Archives (4 files)                                 14 800       3 035          20          32
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            147 072      11 179          38      85 847
  =============================================  ==========  ==========  ==========  ==========


***********************************************************************************************
***                                                                                         ***
***                                     SECTION DETAIL                                      ***
***                                                                                         ***
***********************************************************************************************

Sections by address:

  Range              Symbol or [section] Name         Size  Al  Init  Ac  Object File
  -----------------  -------------------------  ----------  --  ----  --  -----------
  00000000-00000123  __vector_table                    292  512
                                                                Init  RO  startup.s.o
  00000124-000001e1  tick_ms_isr                       190   2  Init  RX  Timer.o
  000001e2-000002bb  dma_isr                           218   2  Init  RX  uart_dma.o
  000002bc-000004a1  uart_isr                          486   2  Init  RX  Uart_Irq.o
  000004a2-0000055f  board_timer_isr                   190   2  Init  RX  board.c.o
  00000560-00000565  nmi_handler                         6   4  Init  RX  startup.s.o
  00000566-00000567  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000568-000006a1  irq_handler_trap                  314   4  Init  RX  trap.c.o
  000006a2-000006a3  ( ALIGN .=.+2 )                     2   -  ----  -   -
  000006a4-0007ffff  ( UNUSED .=.+522588 )         522 588   -  ----  -   -
  00080000-00080013  __SEGGER_RTL_global_locale
                                                        20   4  Init  RW  mbops.o (mbops_timeops_rv32imac_balanced.a)
  00080014-00080017  uiOffsetAddr.1                      4   4  Zero  ZI  SetParaBao.o
  00080018-00083b2f  g_Kalman                       15 128   8  Zero  ZI  main.o
  00083b30-00086307  g_Compen                       10 200   8  Zero  ZI  main.o
  00086308-00086e17  g_Navi                          2 832   8  Zero  ZI  main.o
  00086e18-00087597  g_CmdFullTempCompenData         1 920   8  Zero  ZI  main.o
  00087598-00087c9f  g_GyroANNCompen                 1 800   8  Zero  ZI  main.o
  00087ca0-000883a7  g_AccANNCompen                  1 800   8  Zero  ZI  main.o
  000883a8-00088977  g_ZUPT                          1 488   8  Zero  ZI  main.o
  00088978-00088cb7  g_InertialSysAlign                832   8  Zero  ZI  main.o
  00088cb8-00088e77  g_SysVar                          448   8  Zero  ZI  main.o
  00088e78-00088fd7  g_CmdNormalTempCompenData         352   8  Zero  ZI  main.o
  00088fd8-0008908f  g_Align                           184   8  Zero  ZI  main.o
  00089090-0008910f  g_SelfTest                        128   8  Zero  ZI  main.o
  00089110-0008915f  g_InitBind                         80   8  Zero  ZI  main.o
  00089160-00089177  r_LastGyro                         24   8  Zero  ZI  arithmetic.o
  00089178-0008918f  r_Gyro                             24   8  Zero  ZI  arithmetic.o
  00089190-000891a7  gyro_sum                           24   8  Zero  ZI  arithmetic.o
  000891a8-000891bf  estimated_gyro_bias                24   8  Zero  ZI  arithmetic.o
  000891c0-000891d7  estimated_acc_bias                 24   8  Zero  ZI  arithmetic.o
  000891d8-000891ef  acc_sum                            24   8  Zero  ZI  arithmetic.o
  000891f0-00089207  LastAcc                            24   8  Zero  ZI  arithmetic.o
  00089208-0008921f  Acc                                24   8  Zero  ZI  arithmetic.o
  00089220-0008a959  stSetPara                       5 946   4  Zero  ZI  SetParaBao.o
  0008a95a-0008a95b  tx_counter                          2   2  Zero  ZI  Uart_Irq.o
  0008a95c-0008ad5b  gframeParsebuf                  1 024   4  Zero  ZI  Uart_Irq.o
  0008ad5c-0008ae5b  s_xpi_nor_config                  256   4  Zero  ZI  flash.o
  0008ae5c-0008aeab  InavOutData                        80   4  Zero  ZI  arithmetic.o
  0008aeac-0008aecb  combineData                        32   4  Zero  ZI  arithmetic.o
  0008aecc-0008aee7  stSmi240Data                       28   4  Zero  ZI  protocol.o
  0008aee8-0008af03  ImuData                            28   4  Zero  ZI  main.o
  0008af04-0008af1b  __SEGGER_RTL_aSigTab               24   4  Zero  ZI  execops.o (libc_rv32imac_balanced.a)
  0008af1c-0008af27  control_config                     12   4  Zero  ZI  spi.o
  0008af28-0008af33  control_Slave_config               12   4  Zero  ZI  spi.o
  0008af34-0008af37  timer_cb                            4   4  Zero  ZI  board.c.o
  0008af38-0008af3b  hpm_core_clock                      4   4  Zero  ZI  hpm_clock_drv.c.o
  0008af3c-0008af3f  grxst                               4   4  Zero  ZI  Uart_Irq.o
  0008af40-0008af43  grxlen                              4   4  Zero  ZI  Uart_Irq.o
  0008af44-0008af47  g_console_uart                      4   4  Zero  ZI  hpm_debug_console.c.o
  0008af48-0008af4b  flag.2                              4   4  Zero  ZI  SetParaBao.o
  0008af4c-0008af4f  bias_sample_count                   4   4  Zero  ZI  arithmetic.o
  0008af50-0008af53  bias_estimate_done                  4   4  Zero  ZI  arithmetic.o
  0008af54-0008af57  __SEGGER_RTL_stdout_file            4   4  Zero  ZI  hpm_debug_console.c.o
  0008af58-0008af5b  __SEGGER_RTL_locale_ptr             4   4  Zero  ZI  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0008af5c-0008af5f  __SEGGER_RTL_heap_globals           4   4  Zero  ZI  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0008af60-0008af63  TimeStamp                           4   4  Zero  ZI  main.o
  0008af64-0008af65  tCnt.0                              2   2  Zero  ZI  main.o
  0008af66-0008af66  uart_rx_dma_done                    1   1  Zero  ZI  uart_dma.o
  0008af67-0008af67  g_ucSystemResetFlag                 1   1  Zero  ZI  SetParaBao.o
  0008af68-0008af68  g_UpdateSuccessful                  1   1  Zero  ZI  SetParaBao.o
  0008af69-0008af69  g_StartUpdateFirm                   1   1  Zero  ZI  SetParaBao.o
  0008af6a-0008af6a  fpga_syn                            1   1  Zero  ZI  main.o
  0008af6b-0008af6b  uart_tx_dma_done                    1   1  Init  RW  uart_dma.o
  0008af6c-0008af6f  uiLastBaoInDex.3                    4   4  Init  RW  SetParaBao.o
  0008af70-0008af73  stdout                              4   4  Init  RW  hpm_debug_console.c.o
  0008af74-0008af77  nbr_data_to_send                    4   4  Init  RW  Uart_Irq.o
  0008af78-0008af7b  gbtxcompleted                       4   4  Init  RW  Uart_Irq.o
  0008af7c-0008af7c  g_UpdateBackFlag                    1   1  Init  RW  SetParaBao.o
  0008af7d-0008af7f  ( UNUSED .=.+3 )                    3   -  ----  -   -
  0008af80-0008cf7f  grxbuffer                       8 192   4  None  ZI  Uart_Irq.o
  0008cf80-00090f7f  [.bss.block.heap]              16 384   8  None  ZI  [ Linker created ]
  00090f80-0009bfff  ( UNUSED .=.+45184 )           45 184   -  ----  -   -
  0009c000-0009ffff  [.bss.block.stack]             16 384  16  None  ZI  [ Linker created ]
  8000d000-8000d00f  option                             16   4  Cnst  RO  board.c.o
  8000d010-8000dfff  ( UNUSED .=.+4080 )             4 080   -  ----  -   -
  8000e000-8000e00f  header                             16   4  Cnst  RO  hpm_bootheader.c.o
  8000e010-8000e08f  fw_info                           128   4  Cnst  RO  hpm_bootheader.c.o
  8000e090-8000ffff  ( UNUSED .=.+8048 )             8 048   -  ----  -   -
  80010000-80010065  _start                            102   2  Code  RX  startup.s.o
  80010066-80010067  __SEGGER_RTL_SIGNAL_SIG_DFL
                                                         2   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80010068-8001008f  [.rodata]                          40   8  Cnst  RO  align.o
  80010090-800100af  [.rodata]                          32   8  Cnst  RO  arithmetic.o
  800100b0-80010147  [.rodata]                         152   8  Cnst  RO  navi.o
  80010148-8001046f  [.rodata]                         808   8  Cnst  RO  ZUPT.o
  80010470-80010487  [.rodata]                          24   8  Cnst  RO  compen.o
  80010488-8001048f  [.rodata]                           8   8  Cnst  RO  matvecmath.o
  80010490-800104af  [.rodata]                          32   8  Cnst  RO  protocol.o
  800104b0-800104c7  [.rodata]                          24   8  Cnst  RO  hpm_pllctlv2_drv.c.o
  800104c8-80010527  __SEGGER_RTL_float64_ATan          96   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010528-8001055f  __SEGGER_RTL_float64_Tan           56   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010560-800105cf  __SEGGER_RTL_float64_ASinACos
                                                       112   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  800105d0-8001060f  __SEGGER_RTL_float64_SinCos
                                                        64   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010610-800106af  __SEGGER_RTL_ipow10               160   8  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  800106b0-8001070f  [.srodata.merged.cst8]             96   8  Cnst  RO  [ Linker created ]
  80010710-80010a63  [.rodata]                         852   4  Cnst  RO  flash.o
  80010a64-80010e2b  [.rodata]                         968   4  Cnst  RO  main.o
  80010e2c-80010f77  [.rodata.SetParaTemperCompen]
                                                       332   4  Cnst  RO  SetParaBao.o
  80010f78-80010fa3  [.rodata]                          44   4  Cnst  RO  Smi980.o
  80010fa4-8001134f  [.rodata]                         940   4  Cnst  RO  board.c.o
  80011350-80011363  [.rodata.uart_init]                20   4  Cnst  RO  hpm_uart_drv.c.o
  80011364-80011367  [.rodata]                           4   4  Cnst  RO  hpm_uart_drv.c.o
  80011368-800113a7  [.rodata.exception_handler]
                                                        64   4  Cnst  RO  trap.c.o
  800113a8-800113af  s_wdgs                              8   4  Cnst  RO  hpm_clock_drv.c.o
  800113b0-800113df  [.rodata.clock_get_frequency]
                                                        48   4  Cnst  RO  hpm_clock_drv.c.o
  800113e0-800113ff  [.rodata.get_frequency_for_source]
                                                        32   4  Cnst  RO  hpm_clock_drv.c.o
  80011400-8001142f  [.rodata.clock_set_source_divider]
                                                        48   4  Cnst  RO  hpm_clock_drv.c.o
  80011430-8001152f  __SEGGER_RTL_fdiv_reciprocal_table
                                                       256   4  Cnst  RO  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80011530-800116af  __SEGGER_RTL_aSqrtData            384   4  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  800116b0-80011aaf  __SEGGER_RTL_Moeller_inverse_lut
                                                     1 024   4  Cnst  RO  intops.o (libc_rv32imac_balanced.a)
  80011ab0-80011ac7  __SEGGER_RTL_aPower2f              24   4  Cnst  RO  utilops.o (libc_rv32imac_balanced.a)
  80011ac8-80011ad7  __SEGGER_RTL_hex_lc                16   4  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  80011ad8-80011ae7  __SEGGER_RTL_hex_uc                16   4  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  80011ae8-80011b17  [.rodata.libc.__SEGGER_RTL_vfprintf_short_float_long.str1.4]
                                                        48   4  Cnst  RO  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  80011b18-80011bdf  [.rodata.libc.__SEGGER_RTL_vfprintf_short_float_long]
                                                       200   4  Cnst  RO  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  80011be0-80011beb  __SEGGER_RTL_c_locale              12   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011bec-80011c43  __SEGGER_RTL_c_locale_data
                                                        88   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011c44-80011c63  __SEGGER_RTL_codeset_ascii
                                                        32   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011c64-80011ce3  __SEGGER_RTL_ascii_ctype_map
                                                       128   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011ce4-80011cff  [.srodata.merged.cst4]             28   4  Cnst  RO  [ Linker created ]
  80011d00-80011d19  [.rodata]                          26   4  Cnst  RO  Uart_Irq.o
  80011d1a-80011e5b  ComputeCen                        322   2  Code  RX  align.o
  80011e5c-80011e5d  s_adc_clk_mux_node                  2   4  Cnst  RO  hpm_clock_drv.c.o
  80011e5e-80011f31  InertialSysAlign_Init             212   2  Code  RX  align.o
  80011f32-80011f33  __SEGGER_RTL_SIGNAL_SIG_IGN
                                                         2   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80011f34-80011f35  s_dac_clk_mux_node                  2   4  Cnst  RO  hpm_clock_drv.c.o
  80011f36-80012163  InertialSysAlignCompute           558   2  Code  RX  align.o
  80012164-80012181  [.rodata.libc.__SEGGER_RTL_X_assert.str1.4]
                                                        30   4  Cnst  RO  execops.o (libc_rv32imac_balanced.a)
  80012182-80012273  ComputeVi                         242   2  Code  RX  align.o
  80012274-8001227d  [.rodata.libc.__SEGGER_RTL_find_locale.str1.4]
                                                        10   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8001227e-80012317  ComputeVib0                       154   2  Code  RX  align.o
  80012318-80012319  __SEGGER_RTL_data_utf8_period
                                                         2   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8001231a-80012b93  NavDataOutputSet                2 170   2  Code  RX  arithmetic.o
  80012b94-80012bcd  __SEGGER_RTL_c_locale_day_names
                                                        58   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012bce-80012db5  AlgorithmAct                      488   2  Code  RX  arithmetic.o
  80012db6-80012db7  __SEGGER_RTL_SIGNAL_SIG_ERR
                                                         2   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80012db8-80012db8  __SEGGER_RTL_data_empty_string
                                                         1   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012db9-80012db9  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012dba-80012dfd  INS600mAlgorithmEntry              68   2  Code  RX  arithmetic.o
  80012dfe-80012dff  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80012e00-80012e30  __SEGGER_RTL_c_locale_abbrev_month_names
                                                        49   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012e31-80012e31  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012e32-80012ebb  SysVarDefaultSet                  138   2  Code  RX  navi.o
  80012ebc-80012ed8  __SEGGER_RTL_c_locale_abbrev_day_names
                                                        29   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012ed9-80012ed9  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012eda-80012f85  Sys_Init                          172   2  Code  RX  navi.o
  80012f86-80012f87  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80012f88-80012f94  __SEGGER_RTL_ascii_ctype_mask
                                                        13   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012f95-80012f95  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012f96-800130bf  Navi_Init                         298   2  Code  RX  navi.o
  800130c0-800130c8  __SEGGER_RTL_c_locale_time_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800130c9-800130c9  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800130ca-8001319b  ComputeDelSenbb                   210   2  Code  RX  navi.o
  8001319c-800131a4  __SEGGER_RTL_c_locale_date_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800131a5-800131a5  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800131a6-800134bb  ComputeQ                          790   2  Code  RX  navi.o
  800134bc-800138d6  [.rodata]                       1 051   4  Cnst  RO  spi.o
  800138d7-800138d7  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800138d8-80013992  [.rodata]                         187   4  Cnst  RO  hpm_l1c_drv.c.o
  80013993-80013993  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80013994-800139a2  __SEGGER_RTL_c_locale_date_time_format
                                                        15   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800139a3-800139a3  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800139a4-800139aa  __SEGGER_RTL_c_locale_am_pm_indicator
                                                         7   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800139ab-800139ab  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800139ac-80013a02  __SEGGER_RTL_c_locale_month_names
                                                        87   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80013a03-80013a03  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80013a04-80013ae5  ComputeWnbb                       226   2  Code  RX  navi.o
  80013ae6-80013f6f  QToCnb                          1 162   2  Code  RX  navi.o
  80013f70-800141f5  AttiToCnb                         646   2  Code  RX  navi.o
  800141f6-800143b3  ZUPTInit                          446   2  Code  RX  ZUPT.o
  800143b4-80014a7d  ZUPTDetection                   1 738   2  Code  RX  ZUPT.o
  80014a7e-80014ab7  rom_xpi_nor_erase_sector           58   2  Code  RX  flash.o
  80014ab8-80014af9  rom_xpi_nor_program                66   2  Code  RX  flash.o
  80014afa-80014b3b  norflash_init                      66   2  Code  RX  flash.o
  80014b3c-80014b71  norflash_read                      54   2  Code  RX  flash.o
  80014b72-80014bbf  norflash_read_mem                  78   2  Code  RX  flash.o
  80014bc0-80014bf3  norflash_write                     52   2  Code  RX  flash.o
  80014bf4-80014c1f  norflash_erase_sector              44   2  Code  RX  flash.o
  80014c20-80014ca9  norflash_diagnose_config_locations
                                                       138   2  Code  RX  flash.o
  80014caa-80014d69  norflash_verify_config_option
                                                       192   2  Code  RX  flash.o
  80014d6a-80014df1  norflash_restore_config_option
                                                       136   2  Code  RX  flash.o
  80014df2-80014e4f  spi_transfer_mode_print            94   2  Code  RX  spi.o
  80014e50-80014f4f  SpiInitMaster                     256   2  Code  RX  spi.o
  80014f50-8001501f  SpiInitSlave                      208   2  Code  RX  spi.o
  80015020-800150f9  Timer_Init                        218   2  Code  RX  Timer.o
  800150fa-8001510b  uart_read_byte                     18   2  Code  RX  Uart_Irq.o
  8001510c-8001512d  uart_check_status                  34   2  Code  RX  Uart_Irq.o
  8001512e-80015177  UartIrqSendMsg                     74   2  Code  RX  Uart_Irq.o
  80015178-800154e5  analysisRxdata                    878   2  Code  RX  Uart_Irq.o
  800154e6-80017367  GyroANNCompen_X_Init            7 810   2  Code  RX  AnnTempCompen.o
  80017368-800191e9  GyroANNCompen_Y_Init            7 810   2  Code  RX  AnnTempCompen.o
  800191ea-8001b06b  GyroANNCompen_Z_Init            7 810   2  Code  RX  AnnTempCompen.o
  8001b06c-8001ceed  AccANNCompen_X_Init             7 810   2  Code  RX  AnnTempCompen.o
  8001ceee-8001ed6f  AccANNCompen_Y_Init             7 810   2  Code  RX  AnnTempCompen.o
  8001ed70-80020bf1  AccANNCompen_Z_Init             7 810   2  Code  RX  AnnTempCompen.o
  80020bf2-80020ecb  ANN_Predict                       730   2  Code  RX  AnnTempCompen.o
  80020ecc-80023065  Gyro_Compen_Para_Init           8 602   2  Code  RX  compen.o
  80023066-8002521f  Acc_Compen_Para_Init            8 634   2  Code  RX  compen.o
  80025220-800253cb  GyroCompenCompute                 428   2  Code  RX  compen.o
  800253cc-80025585  AccCompenCompute                  442   2  Code  RX  compen.o
  80025586-80025643  GetTempRangeNum                   190   2  Code  RX  compen.o
  80025644-80025991  ComputeAccTempDiff                846   2  Code  RX  compen.o
  80025992-80025b1f  RTCompenPara                      398   2  Code  RX  compen.o
  80025b20-80025bf1  LinerCompen_60_ANN_Order          210   2  Code  RX  compen.o
  80025bf2-80025c03  ppor_sw_reset                      18   2  Code  RX  FirmwareUpdateFile.o
  80025c04-80025c17  Drv_SystemReset                    20   2  Code  RX  FirmwareUpdateFile.o
  80025c18-80025dcf  startup_diagnostics               440   2  Code  RX  main.o
  80025dd0-800260d5  UserTask                          774   2  Code  RX  main.o
  800260d6-800261e3  main                              270   2  Code  RX  main.o
  800261e4-8002625d  MultiDim_Vec_Dot                  122   2  Code  RX  matvecmath.o
  8002625e-800262b3  Mat_Tr                             86   2  Code  RX  matvecmath.o
  800262b4-800266a5  Mat_Inv                         1 010   2  Code  RX  matvecmath.o
  800266a6-8002698b  Qua_Mul                           742   2  Code  RX  matvecmath.o
  8002698c-800269c5  Check_8bit                         58   2  Code  RX  protocol.o
  800269c6-800269ff  Check_16bit                        58   2  Code  RX  protocol.o
  80026a00-80026a49  xor_check                          74   2  Code  RX  protocol.o
  80026a4a-80026b1d  Smi240UartSend                    212   2  Code  RX  protocol.o
  80026b1e-80026dcb  CombinationSpi2Send               686   2  Code  RX  protocol.o
  80026dcc-80027053  CombinationUartSend22B            648   2  Code  RX  protocol.o
  80027054-80027319  PureSpi2Send                      710   2  Code  RX  protocol.o
  8002731a-8002779b  PureUartSend36B                 1 154   2  Code  RX  protocol.o
  8002779c-80027881  SetParaBaud                       230   2  Code  RX  SetParaBao.o
  80027882-8002795b  SetParaFrequency                  218   2  Code  RX  SetParaBao.o
  8002795c-80027a69  SetParaGnss                       270   2  Code  RX  SetParaBao.o
  80027a6a-80027b77  SetParaAngle                      270   2  Code  RX  SetParaBao.o
  80027b78-80027c85  SetParaVector                     270   2  Code  RX  SetParaBao.o
  80027c86-80027d93  SetParaDeviation                  270   2  Code  RX  SetParaBao.o
  80027d94-80027f09  SetParaGnssInitValue              374   2  Code  RX  SetParaBao.o
  80027f0a-80027fe3  SetParaTime                       218   2  Code  RX  SetParaBao.o
  80027fe4-800281ed  SaveParaToFlash                   522   2  Code  RX  SetParaBao.o
  800281ee-80028407  RestoreFactory                    538   2  Code  RX  SetParaBao.o
  80028408-800287bd  SetParaAll                        950   2  Code  RX  SetParaBao.o
  800287be-800288fb  ReadPara_0                        318   2  Code  RX  SetParaBao.o
  800288fc-800289f1  ReadPara_2                        246   2  Code  RX  SetParaBao.o
  800289f2-80028bc7  ReadPara_3                        470   2  Code  RX  SetParaBao.o
  80028bc8-80028cb5  SetParaCalibration                238   2  Code  RX  SetParaBao.o
  80028cb6-80028d87  SetParaKalmanQ                    210   2  Code  RX  SetParaBao.o
  80028d88-80028e59  SetParaKalmanR                    210   2  Code  RX  SetParaBao.o
  80028e5a-80028f2b  SetParaFilter                     210   2  Code  RX  SetParaBao.o
  80028f2c-8002906d  SetParaUpdateStart                322   2  Code  RX  SetParaBao.o
  8002906e-80029197  SetParaUpdateSend                 298   2  Code  RX  SetParaBao.o
  80029198-80029291  SetParaUpdateEnd                  250   2  Code  RX  SetParaBao.o
  80029292-80029377  TemperCompenGyroNormal            230   2  Code  RX  SetParaBao.o
  80029378-8002945d  TemperCompenAccNormal             230   2  Code  RX  SetParaBao.o
  8002945e-80029543  TemperCompenGyroAll_0             230   2  Code  RX  SetParaBao.o
  80029544-80029629  TemperCompenAccAll_0              230   2  Code  RX  SetParaBao.o
  8002962a-8002970f  TemperCompenGyroAll_1             230   2  Code  RX  SetParaBao.o
  80029710-800297f5  TemperCompenAccAll_1              230   2  Code  RX  SetParaBao.o
  800297f6-800298db  TemperCompenGyroAll_2             230   2  Code  RX  SetParaBao.o
  800298dc-800299c1  TemperCompenAccAll_2              230   2  Code  RX  SetParaBao.o
  800299c2-80029aab  TemperCompenGyroAll_3             234   2  Code  RX  SetParaBao.o
  80029aac-80029b95  TemperCompenAccAll_3              234   2  Code  RX  SetParaBao.o
  80029b96-80029c7f  TemperCompenGyroNerve_X0          234   2  Code  RX  SetParaBao.o
  80029c80-80029d69  TemperCompenGyroNerve_Y0          234   2  Code  RX  SetParaBao.o
  80029d6a-80029e53  TemperCompenGyroNerve_Z0          234   2  Code  RX  SetParaBao.o
  80029e54-80029f3d  TemperCompenAccNerve_X0           234   2  Code  RX  SetParaBao.o
  80029f3e-8002a027  TemperCompenAccNerve_Y0           234   2  Code  RX  SetParaBao.o
  8002a028-8002a111  TemperCompenAccNerve_Z0           234   2  Code  RX  SetParaBao.o
  8002a112-8002a1fb  TemperCompenGyroNerve_X1          234   2  Code  RX  SetParaBao.o
  8002a1fc-8002a2e5  TemperCompenGyroNerve_Y1          234   2  Code  RX  SetParaBao.o
  8002a2e6-8002a3cf  TemperCompenGyroNerve_Z1          234   2  Code  RX  SetParaBao.o
  8002a3d0-8002a4b9  TemperCompenAccNerve_Y1           234   2  Code  RX  SetParaBao.o
  8002a4ba-8002a5a3  TemperCompenAccNerve_Z1           234   2  Code  RX  SetParaBao.o
  8002a5a4-8002a68d  TemperCompenGyroNerve_X2          234   2  Code  RX  SetParaBao.o
  8002a68e-8002a777  TemperCompenGyroNerve_Y2          234   2  Code  RX  SetParaBao.o
  8002a778-8002a861  TemperCompenGyroNerve_Z2          234   2  Code  RX  SetParaBao.o
  8002a862-8002a94b  TemperCompenAccNerve_X2           234   2  Code  RX  SetParaBao.o
  8002a94c-8002aa35  TemperCompenAccNerve_Y2           234   2  Code  RX  SetParaBao.o
  8002aa36-8002ab1f  TemperCompenAccNerve_Z2           234   2  Code  RX  SetParaBao.o
  8002ab20-8002ae91  UartDmaRecSetPara                 882   2  Code  RX  SetParaBao.o
  8002ae92-8002aebb  sysctl_resource_target_is_busy
                                                        42   2  Code  RX  board.c.o
  8002aebc-8002aee1  sysctl_resource_target_get_mode
                                                        38   2  Code  RX  board.c.o
  8002aee2-8002af0b  sysctl_clock_set_preset            42   2  Code  RX  board.c.o
  8002af0c-8002af31  pllctlv2_xtal_set_rampup_time
                                                        38   2  Code  RX  board.c.o
  8002af32-8002af73  board_print_banner                 66   2  Code  RX  board.c.o
  8002af74-8002b021  board_print_clock_freq            174   2  Code  RX  board.c.o
  8002b022-8002b0fb  board_init_usb_dp_dm_pins         218   2  Code  RX  board.c.o
  8002b0fc-8002b113  board_init_uart                    24   2  Code  RX  board.c.o
  8002b114-8002b19d  init_uart_pins                    138   2  Code  RX  pinmux.c.o
  8002b19e-8002b20f  gptmr_channel_get_default_config
                                                       114   2  Code  RX  hpm_gptmr_drv.c.o
  8002b210-8002b341  gptmr_channel_config              306   2  Code  RX  hpm_gptmr_drv.c.o
  8002b342-8002b3b3  pllctlv2_set_postdiv              114   2  Code  RX  hpm_pllctlv2_drv.c.o
  8002b3b4-8002b46d  pllctlv2_get_pll_postdiv_freq_in_hz
                                                       186   2  Code  RX  hpm_pllctlv2_drv.c.o
  8002b46e-8002b48b  spi_get_data_length_in_bytes
                                                        30   2  Code  RX  hpm_spi_drv.c.o
  8002b48c-8002b4c5  spi_get_rx_fifo_valid_data_size
                                                        58   2  Code  RX  hpm_spi_drv.c.o
  8002b4c6-8002b507  spi_write_command                  66   2  Code  RX  hpm_spi_drv.c.o
  8002b508-8002b54d  spi_read_command                   70   2  Code  RX  hpm_spi_drv.c.o
  8002b54e-8002b613  spi_write_data                    198   2  Code  RX  hpm_spi_drv.c.o
  8002b614-8002b6ff  spi_read_data                     236   2  Code  RX  hpm_spi_drv.c.o
  8002b700-8002b851  spi_write_read_data               338   2  Code  RX  hpm_spi_drv.c.o
  8002b852-8002b88f  spi_no_data                        62   2  Code  RX  hpm_spi_drv.c.o
  8002b890-8002b8a9  spi_master_get_default_timing_config
                                                        26   2  Code  RX  hpm_spi_drv.c.o
  8002b8aa-8002b8fb  spi_master_get_default_control_config
                                                        82   2  Code  RX  hpm_spi_drv.c.o
  8002b8fc-8002b935  spi_slave_get_default_control_config
                                                        58   2  Code  RX  hpm_spi_drv.c.o
  8002b936-8002b9d3  spi_master_timing_init            158   2  Code  RX  hpm_spi_drv.c.o
  8002b9d4-8002bb95  uart_calculate_baudrate           450   2  Code  RX  hpm_uart_drv.c.o
  8002bb96-8002bd4d  uart_init                         440   2  Code  RX  hpm_uart_drv.c.o
  8002bd4e-8002bd9b  uart_send_byte                     78   2  Code  RX  hpm_uart_drv.c.o
  8002bd9c-8002be45  _clean_up                         170   2  Code  RX  reset.c.o
  8002be46-8002be57  syscall_handler                    18   2  Code  RX  trap.c.o
  8002be58-8002bed1  hpm_csr_get_core_cycle            122   2  Code  RX  hpm_clock_drv.c.o
  8002bed2-8002bf83  get_frequency_for_source          178   2  Code  RX  hpm_clock_drv.c.o
  8002bf84-8002bfef  get_frequency_for_ip_in_common_group
                                                       108   2  Code  RX  hpm_clock_drv.c.o
  8002bff0-8002c087  get_frequency_for_adc             152   2  Code  RX  hpm_clock_drv.c.o
  8002c088-8002c0bd  get_frequency_for_ewdg             54   2  Code  RX  hpm_clock_drv.c.o
  8002c0be-8002c101  get_frequency_for_cpu              68   2  Code  RX  hpm_clock_drv.c.o
  8002c102-8002c1ff  clock_set_source_divider          254   2  Code  RX  hpm_clock_drv.c.o
  8002c200-8002c239  clock_add_to_group                 58   2  Code  RX  hpm_clock_drv.c.o
  8002c23a-8002c273  clock_remove_from_group            58   2  Code  RX  hpm_clock_drv.c.o
  8002c274-8002c2a9  l1c_dc_enable                      54   2  Code  RX  hpm_l1c_drv.c.o
  8002c2aa-8002c2d7  l1c_ic_enable                      46   2  Code  RX  hpm_l1c_drv.c.o
  8002c2d8-8002c32d  l1c_dc_invalidate                  86   2  Code  RX  hpm_l1c_drv.c.o
  8002c32e-8002c357  sysctl_resource_target_is_busy
                                                        42   2  Code  RX  hpm_sysctl_drv.c.o
  8002c358-8002c379  sysctl_cpu_clock_any_is_busy
                                                        34   2  Code  RX  hpm_sysctl_drv.c.o
  8002c37a-8002c3a7  sysctl_clock_target_is_busy
                                                        46   2  Code  RX  hpm_sysctl_drv.c.o
  8002c3a8-8002c42f  sysctl_config_clock               136   2  Code  RX  hpm_sysctl_drv.c.o
  8002c430-8002c485  system_init                        86   2  Code  RX  system.c.o
  8002c486-8002c4ef  __SEGGER_RTL_xtoa                 106   2  Code  RX  convops.o (libc_rv32imac_balanced.a)
  8002c4f0-8002c50b  itoa                               28   2  Code  RX  convops.o (libc_rv32imac_balanced.a)
  8002c50c-8002c551  fwrite                             70   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  8002c552-8002c575  fputc                              36   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  8002c576-8002c57d  __subsf3                            8   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c57e-8002c587  __subdf3                           10   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c588-8002c735  __addsf3                          430   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c736-8002c76f  __ltsf2                            58   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c770-8002c7b5  __ltdf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c7b6-8002c7fb  __ledf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c7fc-8002c82d  __gtsf2                            50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c82e-8002c873  __gtdf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c874-8002c8b1  __gesf2                            62   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c8b2-8002c8f7  __gedf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c8f8-8002c941  __fixsfsi                          74   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c942-8002c973  __fixunssfsi                       50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c974-8002c9a5  __fixunsdfsi                       50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c9a6-8002ca0b  __floatsisf                       102   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002ca0c-8002ca59  __floatsidf                        78   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002ca5a-8002caaf  __floatunsisf                      86   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002cab0-8002cb59  __floatundisf                     170   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002cb5a-8002cb9f  __extendsfdf2                      70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002cba0-8002cc25  __truncdfsf2                      134   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002cc26-8002cc9f  __SEGGER_RTL_ldouble_to_double
                                                       122   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cca0-8002ccfd  __SEGGER_RTL_float64_PolyEvalP
                                                        94   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002ccfe-8002ce07  __SEGGER_RTL_float64_sin_inline
                                                       266   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002ce08-8002cf15  __SEGGER_RTL_float64_cos_inline
                                                       270   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cf16-8002cf27  __SEGGER_RTL_float32_isnan
                                                        18   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cf28-8002cf35  __SEGGER_RTL_float32_isinf
                                                        14   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cf36-8002cf47  __SEGGER_RTL_float32_isnormal
                                                        18   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cf48-8002cf9d  ldexp.localalias                   86   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cf9e-8002d007  floorf                            106   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002d008-8002d1af  atan                              424   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002d1b0-8002d495  sqrt                              742   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002d496-8002d49b  asin                                6   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002d49c-8002d4c1  __ashldi3                          38   2  Code  RX  intasmops_rv.o (libc_rv32imac_balanced.a)
  8002d4c2-8002d8e1  __udivdi3                       1 056   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002d8e2-8002dd1d  __umoddi3                       1 084   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002dd1e-8002dd27  abs                                10   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002dd28-8002ddad  memcpy                            134   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  8002ddae-8002de07  __SEGGER_RTL_pow10f                90   2  Code  RX  utilops.o (libc_rv32imac_balanced.a)
  8002de08-8002de29  __SEGGER_RTL_prin_flush            34   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002de2a-8002de43  __SEGGER_RTL_pre_padding           26   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002de44-8002de65  __SEGGER_RTL_init_prin_l           34   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002de66-8002de8b  vfprintf                           38   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002de8c-8002deb3  printf                             40   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002deb4-8002dec7  __SEGGER_init_heap                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  8002dec8-8002dedd  __SEGGER_RTL_init_heap             22   2  Code  RX  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  8002dede-8002deeb  __SEGGER_RTL_ascii_toupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002deec-8002def9  __SEGGER_RTL_ascii_towupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002defa-8002df23  __SEGGER_RTL_ascii_mbtowc          42   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002df24-8002e057  ComputeCie                        308   2  Code  RX  align.o
  8002e058-8002e18f  ComputeCib0i                      312   2  Code  RX  align.o
  8002e190-8002e259  FinishInertialSysAlign            202   2  Code  RX  align.o
  8002e25a-8002e40d  ApplyBiasCorrectionToCombineData
                                                       436   2  Code  RX  arithmetic.o
  8002e40e-8002e585  IMUdataPredo                      376   2  Code  RX  arithmetic.o
  8002e586-8002e5d9  IMUdataPredp_algParmCache          84   2  Code  RX  arithmetic.o
  8002e5da-8002e85d  InitialBiasEstimate               644   2  Code  RX  arithmetic.o
  8002e85e-8002e86d  AlgorithmDo                        16   2  Code  RX  arithmetic.o
  8002e86e-8002e89d  Bind_Init                          48   2  Code  RX  navi.o
  8002e89e-8002ea3d  NaviCompute                       416   2  Code  RX  navi.o
  8002ea3e-8002eb9d  ComputeG                          352   2  Code  RX  navi.o
  8002eb9e-8002ec95  ComputeRmRn                       248   2  Code  RX  navi.o
  8002ec96-8002ed09  ComputeWien                       116   2  Code  RX  navi.o
  8002ed0a-8002edb9  ComputeWenn                       176   2  Code  RX  navi.o
  8002edba-8002f025  CnbToAtti                         620   2  Code  RX  navi.o
  8002f026-8002f415  CnbToQ                          1 008   2  Code  RX  navi.o
  8002f416-8002f421  GetZUPTFlag                        12   2  Code  RX  ZUPT.o
  8002f422-8002f47b  ZUPTAngleConstraint                90   2  Code  RX  ZUPT.o
  8002f47c-8002f4b7  rom_xpi_nor_read                   60   2  Code  RX  flash.o
  8002f4b8-8002f4df  rom_xpi_nor_auto_config            40   2  Code  RX  flash.o
  8002f4e0-8002f50b  rom_xpi_nor_get_property           44   2  Code  RX  flash.o
  8002f50c-8002f52d  norflash_get_chip_size             34   2  Code  RX  flash.o
  8002f52e-8002f56d  SpiSlaveSend                       64   2  Code  RX  spi.o
  8002f56e-8002f615  Smi980SpiTransfer                 168   2  Code  RX  spi.o
  8002f616-8002f631  gptmr_enable_irq                   28   2  Code  RX  Timer.o
  8002f632-8002f655  gptmr_check_status                 36   2  Code  RX  Timer.o
  8002f656-8002f669  gptmr_clear_status                 20   2  Code  RX  Timer.o
  8002f66a-8002f695  gptmr_start_counter                44   2  Code  RX  Timer.o
  8002f696-8002f759  dma_check_transfer_status         196   2  Code  RX  uart_dma.o
  8002f75a-8002f771  uart_write_byte                    24   2  Code  RX  Uart_Irq.o
  8002f772-8002f78d  uart_disable_irq                   28   2  Code  RX  Uart_Irq.o
  8002f78e-8002f7a5  uart_enable_irq                    24   2  Code  RX  Uart_Irq.o
  8002f7a6-8002f7bd  uart_get_irq_id                    24   2  Code  RX  Uart_Irq.o
  8002f7be-8002f8c7  UartIrqInit                       266   2  Code  RX  Uart_Irq.o
  8002f8c8-8002f91b  ANNCompen_Init                     84   2  Code  RX  AnnTempCompen.o
  8002f91c-8002fc63  ComputeGyroTempDiff               840   2  Code  RX  compen.o
  8002fc64-8002fc9b  Drv_FlashErase                     56   2  Code  RX  FirmwareUpdateFile.o
  8002fc9c-8002fcdb  Drv_FlashWrite                     64   2  Code  RX  FirmwareUpdateFile.o
  8002fcdc-8002fcf7  Drv_FlashRead                      28   2  Code  RX  FirmwareUpdateFile.o
  8002fcf8-8002fdcd  TaskMange                         214   2  Code  RX  main.o
  8002fdce-8002fe23  Spi2Task                           86   2  Code  RX  main.o
  8002fe24-8002ff3b  Vec_Cross                         280   2  Code  RX  matvecmath.o
  8002ff3c-80030017  Mat_Mul                           220   2  Code  RX  matvecmath.o
  80030018-80030063  Relu                               76   2  Code  RX  matvecmath.o
  80030064-800300cf  GetSmi240Data                     108   2  Code  RX  protocol.o
  800300d0-800303d7  Smi240DataToAlgorithm             776   2  Code  RX  protocol.o
  800303d8-800304a3  Smi240Spi2Send                    204   2  Code  RX  protocol.o
  800304a4-8003075f  CombinationUartSend               700   2  Code  RX  protocol.o
  80030760-80030c5b  PureUartSend                    1 276   2  Code  RX  protocol.o
  80030c5c-80030c93  crc_verify_8bit                    56   2  Code  RX  SetParaBao.o
  80030c94-80030d1f  SendPara_SetHead                  140   2  Code  RX  SetParaBao.o
  80030d20-80030d81  SendPara_SetEnd                    98   2  Code  RX  SetParaBao.o
  80030d82-80030e0d  UpdateStart_SetHead               140   2  Code  RX  SetParaBao.o
  80030e0e-80030e6f  UpdateStart_SetEnd                 98   2  Code  RX  SetParaBao.o
  80030e70-80030efb  UpdateSend_SetHead                140   2  Code  RX  SetParaBao.o
  80030efc-80030f5d  UpdateSend_SetEnd                  98   2  Code  RX  SetParaBao.o
  80030f5e-80030fe9  UpdateEnd_SetHead                 140   2  Code  RX  SetParaBao.o
  80030fea-8003104b  UpdateEnd_SetEnd                   98   2  Code  RX  SetParaBao.o
  8003104c-800310d7  UpdateStop_SetHead                140   2  Code  RX  SetParaBao.o
  800310d8-80031139  UpdateStop_SetEnd                  98   2  Code  RX  SetParaBao.o
  8003113a-800311c5  ReadPara0_SetHead                 140   2  Code  RX  SetParaBao.o
  800311c6-80031227  ReadPara0_SetEnd                   98   2  Code  RX  SetParaBao.o
  80031228-800312b3  ReadPara1_SetHead                 140   2  Code  RX  SetParaBao.o
  800312b4-80031315  ReadPara1_SetEnd                   98   2  Code  RX  SetParaBao.o
  80031316-800313a1  ReadPara2_SetHead                 140   2  Code  RX  SetParaBao.o
  800313a2-80031403  ReadPara2_SetEnd                   98   2  Code  RX  SetParaBao.o
  80031404-8003148f  ReadPara3_SetHead                 140   2  Code  RX  SetParaBao.o
  80031490-800314f3  ReadPara3_SetEnd                  100   2  Code  RX  SetParaBao.o
  800314f4-8003157f  ReadPara4_SetHead                 140   2  Code  RX  SetParaBao.o
  80031580-800315e3  ReadPara4_SetEnd                  100   2  Code  RX  SetParaBao.o
  800315e4-800316bb  SetParaCoord                      216   2  Code  RX  SetParaBao.o
  800316bc-800317e3  ReadParaFromFlash                 296   2  Code  RX  SetParaBao.o
  800317e4-800318db  ReadPara_1                        248   2  Code  RX  SetParaBao.o
  800318dc-80031a0f  ReadPara_4                        308   2  Code  RX  SetParaBao.o
  80031a10-80031afb  ReadPara                          236   2  Code  RX  SetParaBao.o
  80031afc-80031bc7  SetParaGpsType                    204   2  Code  RX  SetParaBao.o
  80031bc8-80031c93  SetParaDataOutType                204   2  Code  RX  SetParaBao.o
  80031c94-80031d5f  SetParaDebugMode                  204   2  Code  RX  SetParaBao.o
  80031d60-80031e2b  SetParaGyroType                   204   2  Code  RX  SetParaBao.o
  80031e2c-80031f87  SetParaFactorGyro                 348   2  Code  RX  SetParaBao.o
  80031f88-800320e3  SetParaFactorAcc                  348   2  Code  RX  SetParaBao.o
  800320e4-8003225f  ParaUpdateHandle                  380   2  Code  RX  SetParaBao.o
  80032260-80032327  SetParaUpdateStop                 200   2  Code  RX  SetParaBao.o
  80032328-8003240d  TemperCompenAccNerve_X1           230   2  Code  RX  SetParaBao.o
  8003240e-8003252f  SetParaTemperCompen               290   2  Code  RX  SetParaBao.o
  80032530-8003259f  Smi980_Init                       112   2  Code  RX  Smi980.o
  800325a0-80032717  Smi980_ReadData                   376   2  Code  RX  Smi980.o
  80032718-80032733  sysctl_resource_any_is_busy
                                                        28   2  Code  RX  board.c.o
  80032734-80032773  sysctl_resource_target_set_mode
                                                        64   2  Code  RX  board.c.o
  80032774-80032797  gptmr_check_status                 36   2  Code  RX  board.c.o
  80032798-800327ab  gptmr_clear_status                 20   2  Code  RX  board.c.o
  800327ac-800327cb  usb_phy_disable_dp_dm_pulldown
                                                        32   2  Code  RX  board.c.o
  800327cc-800327e7  pllctlv2_xtal_is_stable            28   2  Code  RX  board.c.o
  800327e8-80032803  pllctlv2_xtal_is_enabled           28   2  Code  RX  board.c.o
  80032804-80032861  board_init_console                 94   2  Code  RX  board.c.o
  80032862-80032881  board_init                         32   2  Code  RX  board.c.o
  80032882-80032bdd  board_init_clock                  860   2  Code  RX  board.c.o
  80032bde-80032bf1  board_delay_ms                     20   2  Code  RX  board.c.o
  80032bf2-80032c29  board_init_spi_clock               56   2  Code  RX  board.c.o
  80032c2a-80032c3b  board_init_spi_pins                18   2  Code  RX  board.c.o
  80032c3c-80032c3f  board_init_pmp                      4   2  Code  RX  board.c.o
  80032c40-80032d2f  board_init_uart_clock             240   2  Code  RX  board.c.o
  80032d30-80032d7b  init_py_pins_as_pgpio              76   2  Code  RX  pinmux.c.o
  80032d7c-80032def  init_spi_pins                     116   2  Code  RX  pinmux.c.o
  80032df0-80032e55  console_init                      102   2  Code  RX  hpm_debug_console.c.o
  80032e56-80032ed3  __SEGGER_RTL_X_file_write         126   2  Code  RX  hpm_debug_console.c.o
  80032ed4-80032edf  __SEGGER_RTL_X_file_stat           12   2  Code  RX  hpm_debug_console.c.o
  80032ee0-80032eeb  __SEGGER_RTL_X_file_bufsize
                                                        12   2  Code  RX  hpm_debug_console.c.o
  80032eec-80032f33  pcfg_dcdc_set_voltage              72   2  Code  RX  hpm_pcfg_drv.c.o
  80032f34-80032feb  pllctlv2_init_pll_with_freq
                                                       184   2  Code  RX  hpm_pllctlv2_drv.c.o
  80032fec-800330c3  pllctlv2_get_pll_freq_in_hz
                                                       216   2  Code  RX  hpm_pllctlv2_drv.c.o
  800330c4-800330e3  spi_get_data_length_in_bits
                                                        32   2  Code  RX  hpm_spi_drv.c.o
  800330e4-80033123  spi_wait_for_idle_status           64   2  Code  RX  hpm_spi_drv.c.o
  80033124-80033157  spi_write_address                  52   2  Code  RX  hpm_spi_drv.c.o
  80033158-8003319b  spi_master_get_default_format_config
                                                        68   2  Code  RX  hpm_spi_drv.c.o
  8003319c-800331d7  spi_slave_get_default_format_config
                                                        60   2  Code  RX  hpm_spi_drv.c.o
  800331d8-80033257  spi_format_init                   128   2  Code  RX  hpm_spi_drv.c.o
  80033258-80033373  spi_control_init                  284   2  Code  RX  hpm_spi_drv.c.o
  80033374-80033509  spi_transfer                      406   2  Code  RX  hpm_spi_drv.c.o
  8003350a-80033545  uart_modem_config                  60   2  Code  RX  hpm_uart_drv.c.o
  80033546-80033561  uart_disable_irq                   28   2  Code  RX  hpm_uart_drv.c.o
  80033562-80033579  uart_enable_irq                    24   2  Code  RX  hpm_uart_drv.c.o
  8003357a-8003360d  uart_default_config               148   2  Code  RX  hpm_uart_drv.c.o
  8003360e-8003364d  uart_flush                         64   2  Code  RX  hpm_uart_drv.c.o
  8003364e-800336a9  uart_init_rxline_idle_detection
                                                        92   2  Code  RX  hpm_uart_drv.c.o
  800336aa-800336c1  reset_handler                      24   2  Code  RX  reset.c.o
  800336c2-800336c5  _init                               4   2  Code  RX  reset.c.o
  800336c6-800336c9  mchtmr_isr                          4   2  Code  RX  trap.c.o
  800336ca-800336cd  swi_isr                             4   2  Code  RX  trap.c.o
  800336ce-800336f9  exception_handler                  44   2  Code  RX  trap.c.o
  800336fa-8003379b  clock_get_frequency               162   2  Code  RX  hpm_clock_drv.c.o
  8003379c-8003382d  get_frequency_for_dac             146   2  Code  RX  hpm_clock_drv.c.o
  8003382e-80033855  get_frequency_for_pewdg            40   2  Code  RX  hpm_clock_drv.c.o
  80033856-80033881  get_frequency_for_ahb              44   2  Code  RX  hpm_clock_drv.c.o
  80033882-800338af  clock_check_in_group               46   2  Code  RX  hpm_clock_drv.c.o
  800338b0-800338d7  clock_connect_group_to_cpu
                                                        40   2  Code  RX  hpm_clock_drv.c.o
  800338d8-800339af  clock_cpu_delay_ms                216   2  Code  RX  hpm_clock_drv.c.o
  800339b0-800339cd  clock_update_core_clock            30   2  Code  RX  hpm_clock_drv.c.o
  800339ce-80033a81  l1c_op                            180   2  Code  RX  hpm_l1c_drv.c.o
  80033a82-80033a99  l1c_dc_invalidate_all              24   2  Code  RX  hpm_l1c_drv.c.o
  80033a9a-80033b5d  sysctl_enable_group_resource
                                                       196   2  Code  RX  hpm_sysctl_drv.c.o
  80033b5e-80033bd1  sysctl_check_group_resource_enable
                                                       116   2  Code  RX  hpm_sysctl_drv.c.o
  80033bd2-80033c85  sysctl_config_cpu0_domain_clock
                                                       180   2  Code  RX  hpm_sysctl_drv.c.o
  80033c86-80033cb1  enable_plic_feature                44   2  Code  RX  system.c.o
  80033cb2-80033cd5  __SEGGER_RTL_puts_no_nl            36   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80033cd6-80033d09  signal                             52   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80033d0a-80033d65  raise                              92   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80033d66-80033d6f  abort                              10   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80033d70-80033db1  __SEGGER_RTL_X_assert              66   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80033db2-80033dbd  putchar                            12   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  80033dbe-80033df3  puts                               54   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  80033df4-800340cb  __adddf3                          728   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  800340cc-8003417b  __mulsf3                          176   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003417c-8003428b  __muldf3                          272   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003428c-8003438f  __divsf3                          260   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80034390-8003454f  __divdf3                          448   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80034550-8003457b  __eqsf2                            44   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003457c-800345cb  __fixdfsi                          80   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  800345cc-8003462b  __fixunssfdi                       96   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003462c-80034673  __floatunsidf                      72   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80034674-80034697  __SEGGER_RTL_SquareHi_U64          36   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034698-800346f1  __SEGGER_RTL_float64_PolyEvalQ
                                                        90   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  800346f2-80034715  __trunctfsf2                       36   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034716-80034719  __SEGGER_RTL_float32_signbit
                                                         4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003471a-8003475d  ldexpf.localalias                  68   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003475e-80034789  frexpf                             44   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003478a-8003488d  fmodf                             260   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003488e-80034891  sin                                 4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034892-80034895  cos                                 4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034896-80034a11  tan                               380   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034a12-80034bc9  __SEGGER_RTL_float64_asinacos_fpu
                                                       440   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034bca-80034c31  memset                            104   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  80034c32-80034c99  strlen                            104   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  80034c9a-80034d31  strnlen                           152   2  Code  RX  strops.o (libc_rv32imac_balanced.a)
  80034d32-80034d3d  __SEGGER_RTL_stream_write          12   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80034d3e-80034dd9  __SEGGER_RTL_putc                 156   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80034dda-80034e03  __SEGGER_RTL_print_padding
                                                        42   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80034e04-80034e79  vfprintf_l                        118   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80034e7a-80035b0d  __SEGGER_RTL_vfprintf_short_float_long
                                                     3 220   2  Code  RX  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  80035b0e-80035b39  __SEGGER_RTL_ascii_isctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035b3a-80035b49  __SEGGER_RTL_ascii_tolower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035b4a-80035b75  __SEGGER_RTL_ascii_iswctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035b76-80035b85  __SEGGER_RTL_ascii_towlower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035b86-80035b99  __SEGGER_RTL_ascii_wctomb          20   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035b9a-80035bad  __SEGGER_RTL_current_locale
                                                        20   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035bae-80035baf  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80035bb0-80035c03  __SEGGER_init_table__              84   4  Cnst  RO  [ Linker created ]
  80035c04-800362cb  __SEGGER_init_data__            1 736   4  Cnst  RO  [ Linker created ]
  800362cc-800362df  __SEGGER_init_zero                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  800362e0-800362fb  __SEGGER_init_copy                 28   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)


***********************************************************************************************
***                                                                                         ***
***                                       SYMBOL LIST                                       ***
***                                                                                         ***
***********************************************************************************************

RAM function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  board_timer_isr            0x000004A2          54      2  Init  Gb  board.c.o
  default_isr_19             0x0000041A         136      2  Init  Gb  Uart_Irq.o
  default_isr_34             0x00000232         138      2  Init  Gb  uart_dma.o
  default_isr_5              0x0000015A         136      2  Init  Gb  Timer.o
  default_isr_8              0x000004D8         136      2  Init  Gb  board.c.o
  dma_isr                    0x000001E2          80      2  Init  Gb  uart_dma.o
  irq_handler_trap           0x00000568         312      4  Init  Gb  trap.c.o
  tick_ms_isr                0x00000124          54      2  Init  Gb  Timer.o
  uart_isr                   0x000002BC         350      2  Init  Gb  Uart_Irq.o

RAM function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x00000124  tick_ms_isr                        54      2  Init  Gb  Timer.o
  0x0000015A  default_isr_5                     136      2  Init  Gb  Timer.o
  0x000001E2  dma_isr                            80      2  Init  Gb  uart_dma.o
  0x00000232  default_isr_34                    138      2  Init  Gb  uart_dma.o
  0x000002BC  uart_isr                          350      2  Init  Gb  Uart_Irq.o
  0x0000041A  default_isr_19                    136      2  Init  Gb  Uart_Irq.o
  0x000004A2  board_timer_isr                    54      2  Init  Gb  board.c.o
  0x000004D8  default_isr_8                     136      2  Init  Gb  board.c.o
  0x00000568  irq_handler_trap                  312      4  Init  Gb  trap.c.o

RAM function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  uart_isr                          350      2  Init  Gb  Uart_Irq.o
  irq_handler_trap                  312      4  Init  Gb  trap.c.o
  default_isr_34                    138      2  Init  Gb  uart_dma.o
  default_isr_19                    136      2  Init  Gb  Uart_Irq.o
  default_isr_5                     136      2  Init  Gb  Timer.o
  default_isr_8                     136      2  Init  Gb  board.c.o
  dma_isr                            80      2  Init  Gb  uart_dma.o
  board_timer_isr                    54      2  Init  Gb  board.c.o
  tick_ms_isr                        54      2  Init  Gb  Timer.o

Function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  ANNCompen_Init             0x8002F8C8         108      2  Code  Gb  AnnTempCompen.o
  ANN_Predict                0x80020BF2         818      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_X_Init        0x8001B06C       8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Y_Init        0x8001CEEE       8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Z_Init        0x8001ED70       8 134      2  Code  Gb  AnnTempCompen.o
  AccCompenCompute           0x800253CC         490      2  Code  Gb  compen.o
  Acc_Compen_Para_Init       0x80023066       9 246      2  Code  Gb  compen.o
  AlgorithmAct               0x80012BCE         634      2  Code  Gb  arithmetic.o
  AlgorithmDo                0x8002E85E          20      2  Code  Gb  arithmetic.o
  ApplyBiasCorrectionToCombineData
                             0x8002E25A         472      2  Code  Gb  arithmetic.o
  AttiToCnb                  0x80013F70         750      2  Code  Gb  navi.o
  Bind_Init                  0x8002E86E          56      2  Code  Gb  navi.o
  Check_16bit                0x800269C6          58      2  Code  Gb  protocol.o
  Check_8bit                 0x8002698C          58      2  Code  Gb  protocol.o
  CnbToAtti                  0x8002EDBA         728      2  Code  Gb  navi.o
  CnbToQ                     0x8002F026       1 224      2  Code  Gb  navi.o
  CombinationSpi2Send        0x80026B1E         806      2  Code  Gb  protocol.o
  CombinationUartSend        0x800304A4         816      2  Code  Gb  protocol.o
  CombinationUartSend22B     0x80026DCC         758      2  Code  Gb  protocol.o
  ComputeAccTempDiff         0x80025644         886      2  Code  Gb  compen.o
  ComputeCen                 0x80011D1A         354      2  Code  Gb  align.o
  ComputeCib0i               0x8002E058         336      2  Code  Gb  align.o
  ComputeCie                 0x8002DF24         340      2  Code  Gb  align.o
  ComputeDelSenbb            0x800130CA         230      2  Code  Gb  navi.o
  ComputeG                   0x8002EA3E         444      2  Code  Gb  navi.o
  ComputeGyroTempDiff        0x8002F91C         880      2  Code  Gb  compen.o
  ComputeQ                   0x800131A6         926      2  Code  Gb  navi.o
  ComputeRmRn                0x8002EB9E         312      2  Code  Gb  navi.o
  ComputeVi                  0x80012182         266      2  Code  Gb  align.o
  ComputeVib0                0x8001227E         166      2  Code  Gb  align.o
  ComputeWenn                0x8002ED0A         192      2  Code  Gb  navi.o
  ComputeWien                0x8002EC96         140      2  Code  Gb  navi.o
  ComputeWnbb                0x80013A04         238      2  Code  Gb  navi.o
  Drv_FlashErase             0x8002FC64          60      2  Code  Gb  FirmwareUpdateFile.o
  Drv_FlashRead              0x8002FCDC          32      2  Code  Gb  FirmwareUpdateFile.o
  Drv_FlashWrite             0x8002FC9C          68      2  Code  Gb  FirmwareUpdateFile.o
  Drv_SystemReset            0x80025C04          26      2  Code  Gb  FirmwareUpdateFile.o
  FinishInertialSysAlign     0x8002E190         236      2  Code  Gb  align.o
  GetSmi240Data              0x80030064         108      2  Code  Gb  protocol.o
  GetTempRangeNum            0x80025586         194      2  Code  Gb  compen.o
  GetZUPTFlag                0x8002F416          16      2  Code  Gb  ZUPT.o
  GyroANNCompen_X_Init       0x800154E6       8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Y_Init       0x80017368       8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Z_Init       0x800191EA       8 134      2  Code  Gb  AnnTempCompen.o
  GyroCompenCompute          0x80025220         474      2  Code  Gb  compen.o
  Gyro_Compen_Para_Init      0x80020ECC       9 214      2  Code  Gb  compen.o
  IMUdataPredo               0x8002E40E         468      2  Code  Gb  arithmetic.o
  IMUdataPredp_algParmCache  0x8002E586         100      2  Code  Gb  arithmetic.o
  INS600mAlgorithmEntry      0x80012DBA          86      2  Code  Gb  arithmetic.o
  InertialSysAlignCompute    0x80011F36         610      2  Code  Gb  align.o
  InertialSysAlign_Init      0x80011E5E         222      2  Code  Gb  align.o
  InitialBiasEstimate        0x8002E5DA         772      2  Code  Gb  arithmetic.o
  LinerCompen_60_ANN_Order   0x80025B20         222      2  Code  Gb  compen.o
  Mat_Inv                    0x800262B4       1 050      2  Code  Gb  matvecmath.o
  Mat_Mul                    0x8002FF3C         228      2  Code  Gb  matvecmath.o
  Mat_Tr                     0x8002625E          86      2  Code  Gb  matvecmath.o
  MultiDim_Vec_Dot           0x800261E4         130      2  Code  Gb  matvecmath.o
  NavDataOutputSet           0x8001231A       2 318      2  Code  Gb  arithmetic.o
  NaviCompute                0x8002E89E         460      2  Code  Gb  navi.o
  Navi_Init                  0x80012F96         314      2  Code  Gb  navi.o
  ParaUpdateHandle           0x800320E4         400      2  Code  Gb  SetParaBao.o
  PureSpi2Send               0x80027054         730      2  Code  Gb  protocol.o
  PureUartSend               0x80030760       1 296      2  Code  Gb  protocol.o
  PureUartSend36B            0x8002731A       1 238      2  Code  Gb  protocol.o
  QToCnb                     0x80013AE6       1 342      2  Code  Gb  navi.o
  Qua_Mul                    0x800266A6         854      2  Code  Gb  matvecmath.o
  RTCompenPara               0x80025992         422      2  Code  Gb  compen.o
  ReadPara                   0x80031A10         268      2  Code  Gb  SetParaBao.o
  ReadPara0_SetEnd           0x800311C6         112      2  Code  Gb  SetParaBao.o
  ReadPara0_SetHead          0x8003113A         140      2  Code  Gb  SetParaBao.o
  ReadPara1_SetEnd           0x800312B4         112      2  Code  Gb  SetParaBao.o
  ReadPara1_SetHead          0x80031228         140      2  Code  Gb  SetParaBao.o
  ReadPara2_SetEnd           0x800313A2         112      2  Code  Gb  SetParaBao.o
  ReadPara2_SetHead          0x80031316         140      2  Code  Gb  SetParaBao.o
  ReadPara3_SetEnd           0x80031490         112      2  Code  Gb  SetParaBao.o
  ReadPara3_SetHead          0x80031404         140      2  Code  Gb  SetParaBao.o
  ReadPara4_SetEnd           0x80031580         112      2  Code  Gb  SetParaBao.o
  ReadPara4_SetHead          0x800314F4         140      2  Code  Gb  SetParaBao.o
  ReadParaFromFlash          0x800316BC         356      2  Code  Gb  SetParaBao.o
  ReadPara_0                 0x800287BE         366      2  Code  Gb  SetParaBao.o
  ReadPara_1                 0x800317E4         308      2  Code  Gb  SetParaBao.o
  ReadPara_2                 0x800288FC         298      2  Code  Gb  SetParaBao.o
  ReadPara_3                 0x800289F2         534      2  Code  Gb  SetParaBao.o
  ReadPara_4                 0x800318DC         368      2  Code  Gb  SetParaBao.o
  Relu                       0x80030018          80      2  Code  Gb  matvecmath.o
  RestoreFactory             0x800281EE         610      2  Code  Gb  SetParaBao.o
  SaveParaToFlash            0x80027FE4         582      2  Code  Gb  SetParaBao.o
  SendPara_SetEnd            0x80030D20         112      2  Code  Gb  SetParaBao.o
  SendPara_SetHead           0x80030C94         140      2  Code  Gb  SetParaBao.o
  SetParaAll                 0x80028408       1 098      2  Code  Gb  SetParaBao.o
  SetParaAngle               0x80027A6A         314      2  Code  Gb  SetParaBao.o
  SetParaBaud                0x8002779C         274      2  Code  Gb  SetParaBao.o
  SetParaCalibration         0x80028BC8         278      2  Code  Gb  SetParaBao.o
  SetParaCoord               0x800315E4         256      2  Code  Gb  SetParaBao.o
  SetParaDataOutType         0x80031BC8         240      2  Code  Gb  SetParaBao.o
  SetParaDebugMode           0x80031C94         240      2  Code  Gb  SetParaBao.o
  SetParaDeviation           0x80027C86         314      2  Code  Gb  SetParaBao.o
  SetParaFactorAcc           0x80031F88         392      2  Code  Gb  SetParaBao.o
  SetParaFactorGyro          0x80031E2C         392      2  Code  Gb  SetParaBao.o
  SetParaFilter              0x80028E5A         250      2  Code  Gb  SetParaBao.o
  SetParaFrequency           0x80027882         254      2  Code  Gb  SetParaBao.o
  SetParaGnss                0x8002795C         314      2  Code  Gb  SetParaBao.o
  SetParaGnssInitValue       0x80027D94         434      2  Code  Gb  SetParaBao.o
  SetParaGpsType             0x80031AFC         240      2  Code  Gb  SetParaBao.o
  SetParaGyroType            0x80031D60         240      2  Code  Gb  SetParaBao.o
  SetParaKalmanQ             0x80028CB6         250      2  Code  Gb  SetParaBao.o
  SetParaKalmanR             0x80028D88         250      2  Code  Gb  SetParaBao.o
  SetParaTemperCompen        0x8003240E         412      2  Code  Gb  SetParaBao.o
  SetParaTime                0x80027F0A         254      2  Code  Gb  SetParaBao.o
  SetParaUpdateEnd           0x80029198         290      2  Code  Gb  SetParaBao.o
  SetParaUpdateSend          0x8002906E         334      2  Code  Gb  SetParaBao.o
  SetParaUpdateStart         0x80028F2C         358      2  Code  Gb  SetParaBao.o
  SetParaUpdateStop          0x80032260         232      2  Code  Gb  SetParaBao.o
  SetParaVector              0x80027B78         314      2  Code  Gb  SetParaBao.o
  Smi240DataToAlgorithm      0x800300D0         800      2  Code  Gb  protocol.o
  Smi240Spi2Send             0x800303D8         216      2  Code  Gb  protocol.o
  Smi240UartSend             0x80026A4A         226      2  Code  Gb  protocol.o
  Smi980SpiTransfer          0x8002F56E         180      2  Code  Gb  spi.o
  Smi980_Init                0x80032530         152      2  Code  Gb  Smi980.o
  Smi980_ReadData            0x800325A0         436      2  Code  Gb  Smi980.o
  Spi2Task                   0x8002FDCE         100      2  Code  Lc  main.o
  SpiInitMaster              0x80014E50         314      2  Code  Gb  spi.o
  SpiInitSlave               0x80014F50         250      2  Code  Gb  spi.o
  SpiSlaveSend               0x8002F52E          72      2  Code  Gb  spi.o
  SysVarDefaultSet           0x80012E32         174      2  Code  Gb  navi.o
  Sys_Init                   0x80012EDA         242      2  Code  Gb  navi.o
  TaskMange                  0x8002FCF8         244      2  Code  Lc  main.o
  TemperCompenAccAll_0       0x80029544         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_1       0x80029710         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_2       0x800298DC         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_3       0x80029AAC         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X0    0x80029E54         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X1    0x80032328         280      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X2    0x8002A862         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y0    0x80029F3E         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y1    0x8002A3D0         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y2    0x8002A94C         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z0    0x8002A028         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z1    0x8002A4BA         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z2    0x8002AA36         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNormal      0x80029378         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_0      0x8002945E         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_1      0x8002962A         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_2      0x800297F6         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_3      0x800299C2         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X0   0x80029B96         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X1   0x8002A112         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X2   0x8002A5A4         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y0   0x80029C80         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y1   0x8002A1FC         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y2   0x8002A68E         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z0   0x80029D6A         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z1   0x8002A2E6         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z2   0x8002A778         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNormal     0x80029292         278      2  Code  Gb  SetParaBao.o
  Timer_Init                 0x80015020         238      2  Code  Gb  Timer.o
  UartDmaRecSetPara          0x8002AB20         998      2  Code  Gb  SetParaBao.o
  UartIrqInit                0x8002F7BE         296      2  Code  Gb  Uart_Irq.o
  UartIrqSendMsg             0x8001512E          82      2  Code  Gb  Uart_Irq.o
  UpdateEnd_SetEnd           0x80030FEA         112      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetHead          0x80030F5E         140      2  Code  Gb  SetParaBao.o
  UpdateSend_SetEnd          0x80030EFC         112      2  Code  Gb  SetParaBao.o
  UpdateSend_SetHead         0x80030E70         140      2  Code  Gb  SetParaBao.o
  UpdateStart_SetEnd         0x80030E0E         112      2  Code  Gb  SetParaBao.o
  UpdateStart_SetHead        0x80030D82         140      2  Code  Gb  SetParaBao.o
  UpdateStop_SetEnd          0x800310D8         112      2  Code  Gb  SetParaBao.o
  UpdateStop_SetHead         0x8003104C         140      2  Code  Gb  SetParaBao.o
  UserTask                   0x80025DD0         862      2  Code  Lc  main.o
  Vec_Cross                  0x8002FE24         316      2  Code  Gb  matvecmath.o
  ZUPTAngleConstraint        0x8002F422         104      2  Code  Gb  ZUPT.o
  ZUPTDetection              0x800143B4       2 014      2  Code  Gb  ZUPT.o
  ZUPTInit                   0x800141F6         446      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_SIGNAL_SIG_DFL
                             0x80010066           2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_ERR
                             0x80012DB6           2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_IGN
                             0x80011F32           2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SquareHi_U64  0x80034674          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_X_assert      0x80033D70         112      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                             0x80032EE0          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat   0x80032ED4          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_write  0x80032E56         140      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_ascii_isctype
                             0x80035B0E          44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                             0x80035B4A          44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_mbtowc  0x8002DEFA          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_tolower
                             0x80035B3A          16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_toupper
                             0x8002DEDE          14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towlower
                             0x80035B76          16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towupper
                             0x8002DEEC          14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_wctomb  0x80035B86          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_current_locale
                             0x80035B9A          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isinf
                             0x8002CF28          14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnan
                             0x8002CF16          18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnormal
                             0x8002CF36          18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_signbit
                             0x80034716           4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_PolyEvalP
                             0x8002CCA0         110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_PolyEvalQ
                             0x80034698         104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_asinacos_fpu
                             0x80034A12         528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_cos_inline
                             0x8002CE08         354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_sin_inline
                             0x8002CCFE         338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_init_heap     0x8002DEC8          22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_init_prin_l   0x8002DE44          38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ldouble_to_double
                             0x8002CC26         122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_pow10f        0x8002DDAE          98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_pre_padding   0x8002DE2A          30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_prin_flush    0x8002DE08          34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_print_padding
                             0x80034DDA          48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_putc          0x80034D3E         160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_puts_no_nl    0x80033CB2          44      2  Code  Lc  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_stream_write  0x80034D32          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf      0x80034E7A       3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf_short_float_long
                             0x80034E7A       3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_xltoa         0x8002C486         106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_xtoa          0x8002C486         106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  __SEGGER_init_copy         0x800362E0          28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __SEGGER_init_done         0x80010040                  2  Code  Gb  startup.s.o
  __SEGGER_init_heap         0x8002DEB4          26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __SEGGER_init_zero         0x800362CC          20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __adddf3                   0x80033DF4         728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __addsf3                   0x8002C588         430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ashldi3                  0x8002D49C          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  __divdf3                   0x80034390         448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __divsf3                   0x8003428C         260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __eqsf2                    0x80034550          44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __extendsfdf2              0x8002CB5A          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixdfsi                  0x8003457C          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixsfsi                  0x8002C8F8          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunsdfsi               0x8002C974          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfdi               0x800345CC          96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfsi               0x8002C942          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsidf                0x8002CA0C          78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsisf                0x8002C9A6         102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatundisf              0x8002CAB0         170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatunsidf              0x8003462C          72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatunsisf              0x8002CA5A          86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gedf2                    0x8002C8B2          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gesf2                    0x8002C874          62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtdf2                    0x8002C82E          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtsf2                    0x8002C7FC          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ledf2                    0x8002C7B6          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltdf2                    0x8002C770          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltsf2                    0x8002C736          58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __muldf3                   0x8003417C         272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __mulsf3                   0x800340CC         176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __nesf2                    0x80034550          44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subdf3                   0x8002C57E          14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subsf3                   0x8002C576          14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __truncdfsf2               0x8002CBA0         134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __trunctfsf2               0x800346F2          44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __udivdi3                  0x8002D4C2       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  __umoddi3                  0x8002D8E2       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  _clean_up                  0x8002BD9C         170      2  Code  Wk  reset.c.o
  _init                      0x800336C2           4      2  Code  Wk  reset.c.o
  _start                     0x80010000         142      2  Code  Gb  startup.s.o
  abort                      0x80033D66          16      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  abs                        0x8002DD1E          10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  analysisRxdata             0x80015178         882      2  Code  Gb  Uart_Irq.o
  asin                       0x8002D496          10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  atan                       0x8002D008         510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  board_delay_ms             0x80032BDE          24      2  Code  Gb  board.c.o
  board_init                 0x80032862          68      2  Code  Gb  board.c.o
  board_init_clock           0x80032882       1 112      2  Code  Gb  board.c.o
  board_init_console         0x80032804         116      2  Code  Gb  board.c.o
  board_init_pmp             0x80032C3C           4      2  Code  Gb  board.c.o
  board_init_spi_clock       0x80032BF2          64      2  Code  Gb  board.c.o
  board_init_spi_pins        0x80032C2A          24      2  Code  Gb  board.c.o
  board_init_uart            0x8002B0FC          34      2  Code  Gb  board.c.o
  board_init_uart_clock      0x80032C40         288      2  Code  Gb  board.c.o
  board_init_usb_dp_dm_pins  0x8002B022         282      2  Code  Gb  board.c.o
  board_print_banner         0x8002AF32          94      2  Code  Gb  board.c.o
  board_print_clock_freq     0x8002AF74         222      2  Code  Gb  board.c.o
  clock_add_to_group         0x8002C200          62      2  Code  Gb  hpm_clock_drv.c.o
  clock_check_in_group       0x80033882          52      2  Code  Gb  hpm_clock_drv.c.o
  clock_connect_group_to_cpu
                             0x800338B0          40      2  Code  Gb  hpm_clock_drv.c.o
  clock_cpu_delay_ms         0x800338D8         224      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_frequency        0x800336FA         200      2  Code  Gb  hpm_clock_drv.c.o
  clock_remove_from_group    0x8002C23A          62      2  Code  Gb  hpm_clock_drv.c.o
  clock_set_source_divider   0x8002C102         270      2  Code  Gb  hpm_clock_drv.c.o
  clock_update_core_clock    0x800339B0          36      2  Code  Gb  hpm_clock_drv.c.o
  console_init               0x80032DF0         112      2  Code  Gb  hpm_debug_console.c.o
  cos                        0x80034892           8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  crc_verify_8bit            0x80030C5C          56      2  Code  Lc  SetParaBao.o
  dma_check_transfer_status  0x8002F696         196      2  Code  Lc  uart_dma.o
  enable_plic_feature        0x80033C86          44      2  Code  Gb  system.c.o
  exception_handler          0x800336CE          44      2  Code  Wk  trap.c.o
  exit                       0x8001005A           2      2  Code  Gb  startup.s.o
  floorf                     0x8002CF9E         106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fmodf                      0x8003478A         260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fputc                      0x8002C552          42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  frexpf                     0x8003475E          44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fwrite                     0x8002C50C          78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  get_frequency_for_adc      0x8002BFF0         162      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ahb      0x80033856          48      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_cpu      0x8002C0BE          74      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_dac      0x8003379C         156      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ewdg     0x8002C088          58      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ip_in_common_group
                             0x8002BF84         114      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_pewdg    0x8003382E          40      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_source   0x8002BED2         206      2  Code  Gb  hpm_clock_drv.c.o
  gptmr_channel_config       0x8002B210         306      2  Code  Gb  hpm_gptmr_drv.c.o
  gptmr_channel_get_default_config
                             0x8002B19E         114      2  Code  Gb  hpm_gptmr_drv.c.o
  gptmr_check_status         0x8002F632          36      2  Code  Lc  Timer.o
  gptmr_check_status         0x80032774          36      2  Code  Lc  board.c.o
  gptmr_clear_status         0x8002F656          20      2  Code  Lc  Timer.o
  gptmr_clear_status         0x80032798          20      2  Code  Lc  board.c.o
  gptmr_enable_irq           0x8002F616          28      2  Code  Lc  Timer.o
  gptmr_start_counter        0x8002F66A          44      2  Code  Lc  Timer.o
  hpm_csr_get_core_cycle     0x8002BE58         122      2  Code  Lc  hpm_clock_drv.c.o
  init_py_pins_as_pgpio      0x80032D30          76      2  Code  Gb  pinmux.c.o
  init_spi_pins              0x80032D7C         116      2  Code  Gb  pinmux.c.o
  init_uart_pins             0x8002B114         138      2  Code  Gb  pinmux.c.o
  itoa                       0x8002C4F0          34      2  Code  Wk  convops.o (libc_rv32imac_balanced.a)
  l1c_dc_enable              0x8002C274          54      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_dc_invalidate          0x8002C2D8          98      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_dc_invalidate_all      0x80033A82          24      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_ic_enable              0x8002C2AA          46      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_op                     0x800339CE         180      2  Code  Lc  hpm_l1c_drv.c.o
  ldexp                      0x8002CF48          86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexp.localalias           0x8002CF48          86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ldexpf                     0x8003471A          68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexpf.localalias          0x8003471A          68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  main                       0x800260D6         410      2  Code  Gb  main.o
  mchtmr_isr                 0x800336C6           4      2  Code  Wk  trap.c.o
  memcpy                     0x8002DD28         134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  memset                     0x80034BCA         104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  norflash_diagnose_config_locations
                             0x80014C20         214      2  Code  Gb  flash.o
  norflash_erase_sector      0x80014BF4          50      2  Code  Gb  flash.o
  norflash_get_chip_size     0x8002F50C          40      2  Code  Gb  flash.o
  norflash_init              0x80014AFA          78      2  Code  Gb  flash.o
  norflash_read              0x80014B3C          58      2  Code  Gb  flash.o
  norflash_read_mem          0x80014B72          86      2  Code  Gb  flash.o
  norflash_restore_config_option
                             0x80014D6A         214      2  Code  Gb  flash.o
  norflash_verify_config_option
                             0x80014CAA         242      2  Code  Gb  flash.o
  norflash_write             0x80014BC0          58      2  Code  Gb  flash.o
  pcfg_dcdc_set_voltage      0x80032EEC          72      2  Code  Gb  hpm_pcfg_drv.c.o
  pllctlv2_get_pll_freq_in_hz
                             0x80032FEC         248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_get_pll_postdiv_freq_in_hz
                             0x8002B3B4         222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_init_pll_with_freq
                             0x80032F34         184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_set_postdiv       0x8002B342         114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_xtal_is_enabled   0x800327E8          28      2  Code  Lc  board.c.o
  pllctlv2_xtal_is_stable    0x800327CC          28      2  Code  Lc  board.c.o
  pllctlv2_xtal_set_rampup_time
                             0x8002AF0C          38      2  Code  Lc  board.c.o
  ppor_sw_reset              0x80025BF2          18      2  Code  Lc  FirmwareUpdateFile.o
  printf                     0x8002DE8C          46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  putchar                    0x80033DB2          16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  puts                       0x80033DBE          68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  raise                      0x80033D0A         108      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  reset_handler              0x800336AA          32      2  Code  Wk  reset.c.o
  rom_xpi_nor_auto_config    0x8002F4B8          40      2  Code  Lc  flash.o
  rom_xpi_nor_erase_sector   0x80014A7E          58      2  Code  Lc  flash.o
  rom_xpi_nor_get_property   0x8002F4E0          44      2  Code  Lc  flash.o
  rom_xpi_nor_program        0x80014AB8          66      2  Code  Lc  flash.o
  rom_xpi_nor_read           0x8002F47C          60      2  Code  Lc  flash.o
  signal                     0x80033CD6          52      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  sin                        0x8003488E           8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  spi_control_init           0x80033258         284      2  Code  Gb  hpm_spi_drv.c.o
  spi_format_init            0x800331D8         128      2  Code  Gb  hpm_spi_drv.c.o
  spi_get_data_length_in_bits
                             0x800330C4          32      2  Code  Lc  hpm_spi_drv.c.o
  spi_get_data_length_in_bytes
                             0x8002B46E          34      2  Code  Lc  hpm_spi_drv.c.o
  spi_get_rx_fifo_valid_data_size
                             0x8002B48C          58      2  Code  Lc  hpm_spi_drv.c.o
  spi_master_get_default_control_config
                             0x8002B8AA          82      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_get_default_format_config
                             0x80033158          68      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_get_default_timing_config
                             0x8002B890          26      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_timing_init     0x8002B936         158      2  Code  Gb  hpm_spi_drv.c.o
  spi_no_data                0x8002B852          62      2  Code  Lc  hpm_spi_drv.c.o
  spi_read_command           0x8002B508          70      2  Code  Gb  hpm_spi_drv.c.o
  spi_read_data              0x8002B614         242      2  Code  Gb  hpm_spi_drv.c.o
  spi_slave_get_default_control_config
                             0x8002B8FC          58      2  Code  Gb  hpm_spi_drv.c.o
  spi_slave_get_default_format_config
                             0x8003319C          60      2  Code  Gb  hpm_spi_drv.c.o
  spi_transfer               0x80033374         460      2  Code  Gb  hpm_spi_drv.c.o
  spi_transfer_mode_print    0x80014DF2         102      2  Code  Gb  spi.o
  spi_wait_for_idle_status   0x800330E4          64      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_address          0x80033124          52      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_command          0x8002B4C6          66      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_data             0x8002B54E         198      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_read_data        0x8002B700         338      2  Code  Gb  hpm_spi_drv.c.o
  sqrt                       0x8002D1B0         778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  start                      0x80010054                  2  Code  Gb  startup.s.o
  startup_diagnostics        0x80025C18         674      2  Code  Lc  main.o
  strlen                     0x80034C32         104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  strnlen                    0x80034C9A         152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  swi_isr                    0x800336CA           4      2  Code  Wk  trap.c.o
  syscall_handler            0x8002BE46          18      2  Code  Wk  trap.c.o
  sysctl_check_group_resource_enable
                             0x80033B5E         116      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_clock_set_preset    0x8002AEE2          42      2  Code  Lc  board.c.o
  sysctl_clock_target_is_busy
                             0x8002C37A          46      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_config_clock        0x8002C3A8         142      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_config_cpu0_domain_clock
                             0x80033BD2         188      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_cpu_clock_any_is_busy
                             0x8002C358          34      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_enable_group_resource
                             0x80033A9A         200      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_resource_any_is_busy
                             0x80032718          28      2  Code  Lc  board.c.o
  sysctl_resource_target_get_mode
                             0x8002AEBC          38      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                             0x8002AE92          42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                             0x8002C32E          42      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_resource_target_set_mode
                             0x80032734          64      2  Code  Lc  board.c.o
  system_init                0x8002C430          90      2  Code  Wk  system.c.o
  tan                        0x80034896         488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  uart_calculate_baudrate    0x8002B9D4         526      2  Code  Lc  hpm_uart_drv.c.o
  uart_check_status          0x8001510C          34      2  Code  Lc  Uart_Irq.o
  uart_default_config        0x8003357A         148      2  Code  Gb  hpm_uart_drv.c.o
  uart_disable_irq           0x8002F772          28      2  Code  Lc  Uart_Irq.o
  uart_disable_irq           0x80033546          28      2  Code  Lc  hpm_uart_drv.c.o
  uart_enable_irq            0x8002F78E          24      2  Code  Lc  Uart_Irq.o
  uart_enable_irq            0x80033562          24      2  Code  Lc  hpm_uart_drv.c.o
  uart_flush                 0x8003360E          64      2  Code  Gb  hpm_uart_drv.c.o
  uart_get_irq_id            0x8002F7A6          24      2  Code  Lc  Uart_Irq.o
  uart_init                  0x8002BB96         454      2  Code  Gb  hpm_uart_drv.c.o
  uart_init_rxline_idle_detection
                             0x8003364E         104      2  Code  Gb  hpm_uart_drv.c.o
  uart_modem_config          0x8003350A          60      2  Code  Lc  hpm_uart_drv.c.o
  uart_read_byte             0x800150FA          18      2  Code  Lc  Uart_Irq.o
  uart_send_byte             0x8002BD4E          78      2  Code  Gb  hpm_uart_drv.c.o
  uart_write_byte            0x8002F75A          24      2  Code  Lc  Uart_Irq.o
  usb_phy_disable_dp_dm_pulldown
                             0x800327AC          32      2  Code  Lc  board.c.o
  vfprintf                   0x8002DE66          46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  vfprintf_l                 0x80034E04         132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  xor_check                  0x80026A00          74      2  Code  Gb  protocol.o

Function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x80010000  _start                            142      2  Code  Gb  startup.s.o
  0x80010040  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  0x80010054  start                                      2  Code  Gb  startup.s.o
  0x8001005A  exit                                2      2  Code  Gb  startup.s.o
  0x80010066  __SEGGER_RTL_SIGNAL_SIG_DFL
                                                  2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80011D1A  ComputeCen                        354      2  Code  Gb  align.o
  0x80011E5E  InertialSysAlign_Init             222      2  Code  Gb  align.o
  0x80011F32  __SEGGER_RTL_SIGNAL_SIG_IGN
                                                  2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80011F36  InertialSysAlignCompute           610      2  Code  Gb  align.o
  0x80012182  ComputeVi                         266      2  Code  Gb  align.o
  0x8001227E  ComputeVib0                       166      2  Code  Gb  align.o
  0x8001231A  NavDataOutputSet                2 318      2  Code  Gb  arithmetic.o
  0x80012BCE  AlgorithmAct                      634      2  Code  Gb  arithmetic.o
  0x80012DB6  __SEGGER_RTL_SIGNAL_SIG_ERR
                                                  2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80012DBA  INS600mAlgorithmEntry              86      2  Code  Gb  arithmetic.o
  0x80012E32  SysVarDefaultSet                  174      2  Code  Gb  navi.o
  0x80012EDA  Sys_Init                          242      2  Code  Gb  navi.o
  0x80012F96  Navi_Init                         314      2  Code  Gb  navi.o
  0x800130CA  ComputeDelSenbb                   230      2  Code  Gb  navi.o
  0x800131A6  ComputeQ                          926      2  Code  Gb  navi.o
  0x80013A04  ComputeWnbb                       238      2  Code  Gb  navi.o
  0x80013AE6  QToCnb                          1 342      2  Code  Gb  navi.o
  0x80013F70  AttiToCnb                         750      2  Code  Gb  navi.o
  0x800141F6  ZUPTInit                          446      2  Code  Gb  ZUPT.o
  0x800143B4  ZUPTDetection                   2 014      2  Code  Gb  ZUPT.o
  0x80014A7E  rom_xpi_nor_erase_sector           58      2  Code  Lc  flash.o
  0x80014AB8  rom_xpi_nor_program                66      2  Code  Lc  flash.o
  0x80014AFA  norflash_init                      78      2  Code  Gb  flash.o
  0x80014B3C  norflash_read                      58      2  Code  Gb  flash.o
  0x80014B72  norflash_read_mem                  86      2  Code  Gb  flash.o
  0x80014BC0  norflash_write                     58      2  Code  Gb  flash.o
  0x80014BF4  norflash_erase_sector              50      2  Code  Gb  flash.o
  0x80014C20  norflash_diagnose_config_locations
                                                214      2  Code  Gb  flash.o
  0x80014CAA  norflash_verify_config_option
                                                242      2  Code  Gb  flash.o
  0x80014D6A  norflash_restore_config_option
                                                214      2  Code  Gb  flash.o
  0x80014DF2  spi_transfer_mode_print           102      2  Code  Gb  spi.o
  0x80014E50  SpiInitMaster                     314      2  Code  Gb  spi.o
  0x80014F50  SpiInitSlave                      250      2  Code  Gb  spi.o
  0x80015020  Timer_Init                        238      2  Code  Gb  Timer.o
  0x800150FA  uart_read_byte                     18      2  Code  Lc  Uart_Irq.o
  0x8001510C  uart_check_status                  34      2  Code  Lc  Uart_Irq.o
  0x8001512E  UartIrqSendMsg                     82      2  Code  Gb  Uart_Irq.o
  0x80015178  analysisRxdata                    882      2  Code  Gb  Uart_Irq.o
  0x800154E6  GyroANNCompen_X_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x80017368  GyroANNCompen_Y_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x800191EA  GyroANNCompen_Z_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x8001B06C  AccANNCompen_X_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x8001CEEE  AccANNCompen_Y_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x8001ED70  AccANNCompen_Z_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x80020BF2  ANN_Predict                       818      2  Code  Gb  AnnTempCompen.o
  0x80020ECC  Gyro_Compen_Para_Init           9 214      2  Code  Gb  compen.o
  0x80023066  Acc_Compen_Para_Init            9 246      2  Code  Gb  compen.o
  0x80025220  GyroCompenCompute                 474      2  Code  Gb  compen.o
  0x800253CC  AccCompenCompute                  490      2  Code  Gb  compen.o
  0x80025586  GetTempRangeNum                   194      2  Code  Gb  compen.o
  0x80025644  ComputeAccTempDiff                886      2  Code  Gb  compen.o
  0x80025992  RTCompenPara                      422      2  Code  Gb  compen.o
  0x80025B20  LinerCompen_60_ANN_Order          222      2  Code  Gb  compen.o
  0x80025BF2  ppor_sw_reset                      18      2  Code  Lc  FirmwareUpdateFile.o
  0x80025C04  Drv_SystemReset                    26      2  Code  Gb  FirmwareUpdateFile.o
  0x80025C18  startup_diagnostics               674      2  Code  Lc  main.o
  0x80025DD0  UserTask                          862      2  Code  Lc  main.o
  0x800260D6  main                              410      2  Code  Gb  main.o
  0x800261E4  MultiDim_Vec_Dot                  130      2  Code  Gb  matvecmath.o
  0x8002625E  Mat_Tr                             86      2  Code  Gb  matvecmath.o
  0x800262B4  Mat_Inv                         1 050      2  Code  Gb  matvecmath.o
  0x800266A6  Qua_Mul                           854      2  Code  Gb  matvecmath.o
  0x8002698C  Check_8bit                         58      2  Code  Gb  protocol.o
  0x800269C6  Check_16bit                        58      2  Code  Gb  protocol.o
  0x80026A00  xor_check                          74      2  Code  Gb  protocol.o
  0x80026A4A  Smi240UartSend                    226      2  Code  Gb  protocol.o
  0x80026B1E  CombinationSpi2Send               806      2  Code  Gb  protocol.o
  0x80026DCC  CombinationUartSend22B            758      2  Code  Gb  protocol.o
  0x80027054  PureSpi2Send                      730      2  Code  Gb  protocol.o
  0x8002731A  PureUartSend36B                 1 238      2  Code  Gb  protocol.o
  0x8002779C  SetParaBaud                       274      2  Code  Gb  SetParaBao.o
  0x80027882  SetParaFrequency                  254      2  Code  Gb  SetParaBao.o
  0x8002795C  SetParaGnss                       314      2  Code  Gb  SetParaBao.o
  0x80027A6A  SetParaAngle                      314      2  Code  Gb  SetParaBao.o
  0x80027B78  SetParaVector                     314      2  Code  Gb  SetParaBao.o
  0x80027C86  SetParaDeviation                  314      2  Code  Gb  SetParaBao.o
  0x80027D94  SetParaGnssInitValue              434      2  Code  Gb  SetParaBao.o
  0x80027F0A  SetParaTime                       254      2  Code  Gb  SetParaBao.o
  0x80027FE4  SaveParaToFlash                   582      2  Code  Gb  SetParaBao.o
  0x800281EE  RestoreFactory                    610      2  Code  Gb  SetParaBao.o
  0x80028408  SetParaAll                      1 098      2  Code  Gb  SetParaBao.o
  0x800287BE  ReadPara_0                        366      2  Code  Gb  SetParaBao.o
  0x800288FC  ReadPara_2                        298      2  Code  Gb  SetParaBao.o
  0x800289F2  ReadPara_3                        534      2  Code  Gb  SetParaBao.o
  0x80028BC8  SetParaCalibration                278      2  Code  Gb  SetParaBao.o
  0x80028CB6  SetParaKalmanQ                    250      2  Code  Gb  SetParaBao.o
  0x80028D88  SetParaKalmanR                    250      2  Code  Gb  SetParaBao.o
  0x80028E5A  SetParaFilter                     250      2  Code  Gb  SetParaBao.o
  0x80028F2C  SetParaUpdateStart                358      2  Code  Gb  SetParaBao.o
  0x8002906E  SetParaUpdateSend                 334      2  Code  Gb  SetParaBao.o
  0x80029198  SetParaUpdateEnd                  290      2  Code  Gb  SetParaBao.o
  0x80029292  TemperCompenGyroNormal            278      2  Code  Gb  SetParaBao.o
  0x80029378  TemperCompenAccNormal             278      2  Code  Gb  SetParaBao.o
  0x8002945E  TemperCompenGyroAll_0             278      2  Code  Gb  SetParaBao.o
  0x80029544  TemperCompenAccAll_0              278      2  Code  Gb  SetParaBao.o
  0x8002962A  TemperCompenGyroAll_1             278      2  Code  Gb  SetParaBao.o
  0x80029710  TemperCompenAccAll_1              278      2  Code  Gb  SetParaBao.o
  0x800297F6  TemperCompenGyroAll_2             278      2  Code  Gb  SetParaBao.o
  0x800298DC  TemperCompenAccAll_2              278      2  Code  Gb  SetParaBao.o
  0x800299C2  TemperCompenGyroAll_3             282      2  Code  Gb  SetParaBao.o
  0x80029AAC  TemperCompenAccAll_3              282      2  Code  Gb  SetParaBao.o
  0x80029B96  TemperCompenGyroNerve_X0          282      2  Code  Gb  SetParaBao.o
  0x80029C80  TemperCompenGyroNerve_Y0          282      2  Code  Gb  SetParaBao.o
  0x80029D6A  TemperCompenGyroNerve_Z0          282      2  Code  Gb  SetParaBao.o
  0x80029E54  TemperCompenAccNerve_X0           282      2  Code  Gb  SetParaBao.o
  0x80029F3E  TemperCompenAccNerve_Y0           282      2  Code  Gb  SetParaBao.o
  0x8002A028  TemperCompenAccNerve_Z0           282      2  Code  Gb  SetParaBao.o
  0x8002A112  TemperCompenGyroNerve_X1          282      2  Code  Gb  SetParaBao.o
  0x8002A1FC  TemperCompenGyroNerve_Y1          282      2  Code  Gb  SetParaBao.o
  0x8002A2E6  TemperCompenGyroNerve_Z1          282      2  Code  Gb  SetParaBao.o
  0x8002A3D0  TemperCompenAccNerve_Y1           282      2  Code  Gb  SetParaBao.o
  0x8002A4BA  TemperCompenAccNerve_Z1           282      2  Code  Gb  SetParaBao.o
  0x8002A5A4  TemperCompenGyroNerve_X2          282      2  Code  Gb  SetParaBao.o
  0x8002A68E  TemperCompenGyroNerve_Y2          282      2  Code  Gb  SetParaBao.o
  0x8002A778  TemperCompenGyroNerve_Z2          282      2  Code  Gb  SetParaBao.o
  0x8002A862  TemperCompenAccNerve_X2           282      2  Code  Gb  SetParaBao.o
  0x8002A94C  TemperCompenAccNerve_Y2           282      2  Code  Gb  SetParaBao.o
  0x8002AA36  TemperCompenAccNerve_Z2           282      2  Code  Gb  SetParaBao.o
  0x8002AB20  UartDmaRecSetPara                 998      2  Code  Gb  SetParaBao.o
  0x8002AE92  sysctl_resource_target_is_busy
                                                 42      2  Code  Lc  board.c.o
  0x8002AEBC  sysctl_resource_target_get_mode
                                                 38      2  Code  Lc  board.c.o
  0x8002AEE2  sysctl_clock_set_preset            42      2  Code  Lc  board.c.o
  0x8002AF0C  pllctlv2_xtal_set_rampup_time
                                                 38      2  Code  Lc  board.c.o
  0x8002AF32  board_print_banner                 94      2  Code  Gb  board.c.o
  0x8002AF74  board_print_clock_freq            222      2  Code  Gb  board.c.o
  0x8002B022  board_init_usb_dp_dm_pins         282      2  Code  Gb  board.c.o
  0x8002B0FC  board_init_uart                    34      2  Code  Gb  board.c.o
  0x8002B114  init_uart_pins                    138      2  Code  Gb  pinmux.c.o
  0x8002B19E  gptmr_channel_get_default_config
                                                114      2  Code  Gb  hpm_gptmr_drv.c.o
  0x8002B210  gptmr_channel_config              306      2  Code  Gb  hpm_gptmr_drv.c.o
  0x8002B342  pllctlv2_set_postdiv              114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x8002B3B4  pllctlv2_get_pll_postdiv_freq_in_hz
                                                222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x8002B46E  spi_get_data_length_in_bytes
                                                 34      2  Code  Lc  hpm_spi_drv.c.o
  0x8002B48C  spi_get_rx_fifo_valid_data_size
                                                 58      2  Code  Lc  hpm_spi_drv.c.o
  0x8002B4C6  spi_write_command                  66      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B508  spi_read_command                   70      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B54E  spi_write_data                    198      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B614  spi_read_data                     242      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B700  spi_write_read_data               338      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B852  spi_no_data                        62      2  Code  Lc  hpm_spi_drv.c.o
  0x8002B890  spi_master_get_default_timing_config
                                                 26      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B8AA  spi_master_get_default_control_config
                                                 82      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B8FC  spi_slave_get_default_control_config
                                                 58      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B936  spi_master_timing_init            158      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B9D4  uart_calculate_baudrate           526      2  Code  Lc  hpm_uart_drv.c.o
  0x8002BB96  uart_init                         454      2  Code  Gb  hpm_uart_drv.c.o
  0x8002BD4E  uart_send_byte                     78      2  Code  Gb  hpm_uart_drv.c.o
  0x8002BD9C  _clean_up                         170      2  Code  Wk  reset.c.o
  0x8002BE46  syscall_handler                    18      2  Code  Wk  trap.c.o
  0x8002BE58  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  0x8002BED2  get_frequency_for_source          206      2  Code  Gb  hpm_clock_drv.c.o
  0x8002BF84  get_frequency_for_ip_in_common_group
                                                114      2  Code  Lc  hpm_clock_drv.c.o
  0x8002BFF0  get_frequency_for_adc             162      2  Code  Lc  hpm_clock_drv.c.o
  0x8002C088  get_frequency_for_ewdg             58      2  Code  Lc  hpm_clock_drv.c.o
  0x8002C0BE  get_frequency_for_cpu              74      2  Code  Lc  hpm_clock_drv.c.o
  0x8002C102  clock_set_source_divider          270      2  Code  Gb  hpm_clock_drv.c.o
  0x8002C200  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  0x8002C23A  clock_remove_from_group            62      2  Code  Gb  hpm_clock_drv.c.o
  0x8002C274  l1c_dc_enable                      54      2  Code  Gb  hpm_l1c_drv.c.o
  0x8002C2AA  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.c.o
  0x8002C2D8  l1c_dc_invalidate                  98      2  Code  Gb  hpm_l1c_drv.c.o
  0x8002C32E  sysctl_resource_target_is_busy
                                                 42      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002C358  sysctl_cpu_clock_any_is_busy
                                                 34      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002C37A  sysctl_clock_target_is_busy
                                                 46      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002C3A8  sysctl_config_clock               142      2  Code  Gb  hpm_sysctl_drv.c.o
  0x8002C430  system_init                        90      2  Code  Wk  system.c.o
  0x8002C486  __SEGGER_RTL_xtoa                 106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  0x8002C486  __SEGGER_RTL_xltoa                106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  0x8002C4F0  itoa                               34      2  Code  Wk  convops.o (libc_rv32imac_balanced.a)
  0x8002C50C  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x8002C552  fputc                              42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x8002C576  __subsf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C57E  __subdf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C588  __addsf3                          430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C736  __ltsf2                            58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C770  __ltdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C7B6  __ledf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C7FC  __gtsf2                            50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C82E  __gtdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C874  __gesf2                            62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C8B2  __gedf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C8F8  __fixsfsi                          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C942  __fixunssfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C974  __fixunsdfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C9A6  __floatsisf                       102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002CA0C  __floatsidf                        78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002CA5A  __floatunsisf                      86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002CAB0  __floatundisf                     170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002CB5A  __extendsfdf2                      70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002CBA0  __truncdfsf2                      134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002CC26  __SEGGER_RTL_ldouble_to_double
                                                122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002CCA0  __SEGGER_RTL_float64_PolyEvalP
                                                110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002CCFE  __SEGGER_RTL_float64_sin_inline
                                                338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002CE08  __SEGGER_RTL_float64_cos_inline
                                                354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002CF16  __SEGGER_RTL_float32_isnan
                                                 18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CF28  __SEGGER_RTL_float32_isinf
                                                 14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CF36  __SEGGER_RTL_float32_isnormal
                                                 18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CF48  ldexp.localalias                   86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002CF48  ldexp                              86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CF9E  floorf                            106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002D008  atan                              510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002D1B0  sqrt                              778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002D496  asin                               10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002D49C  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002D4C2  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002D8E2  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002DD1E  abs                                10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002DD28  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002DDAE  __SEGGER_RTL_pow10f                98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  0x8002DE08  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002DE2A  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002DE44  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002DE66  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002DE8C  printf                             46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  0x8002DEB4  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  0x8002DEC8  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0x8002DEDE  __SEGGER_RTL_ascii_toupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002DEEC  __SEGGER_RTL_ascii_towupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002DEFA  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002DF24  ComputeCie                        340      2  Code  Gb  align.o
  0x8002E058  ComputeCib0i                      336      2  Code  Gb  align.o
  0x8002E190  FinishInertialSysAlign            236      2  Code  Gb  align.o
  0x8002E25A  ApplyBiasCorrectionToCombineData
                                                472      2  Code  Gb  arithmetic.o
  0x8002E40E  IMUdataPredo                      468      2  Code  Gb  arithmetic.o
  0x8002E586  IMUdataPredp_algParmCache         100      2  Code  Gb  arithmetic.o
  0x8002E5DA  InitialBiasEstimate               772      2  Code  Gb  arithmetic.o
  0x8002E85E  AlgorithmDo                        20      2  Code  Gb  arithmetic.o
  0x8002E86E  Bind_Init                          56      2  Code  Gb  navi.o
  0x8002E89E  NaviCompute                       460      2  Code  Gb  navi.o
  0x8002EA3E  ComputeG                          444      2  Code  Gb  navi.o
  0x8002EB9E  ComputeRmRn                       312      2  Code  Gb  navi.o
  0x8002EC96  ComputeWien                       140      2  Code  Gb  navi.o
  0x8002ED0A  ComputeWenn                       192      2  Code  Gb  navi.o
  0x8002EDBA  CnbToAtti                         728      2  Code  Gb  navi.o
  0x8002F026  CnbToQ                          1 224      2  Code  Gb  navi.o
  0x8002F416  GetZUPTFlag                        16      2  Code  Gb  ZUPT.o
  0x8002F422  ZUPTAngleConstraint               104      2  Code  Gb  ZUPT.o
  0x8002F47C  rom_xpi_nor_read                   60      2  Code  Lc  flash.o
  0x8002F4B8  rom_xpi_nor_auto_config            40      2  Code  Lc  flash.o
  0x8002F4E0  rom_xpi_nor_get_property           44      2  Code  Lc  flash.o
  0x8002F50C  norflash_get_chip_size             40      2  Code  Gb  flash.o
  0x8002F52E  SpiSlaveSend                       72      2  Code  Gb  spi.o
  0x8002F56E  Smi980SpiTransfer                 180      2  Code  Gb  spi.o
  0x8002F616  gptmr_enable_irq                   28      2  Code  Lc  Timer.o
  0x8002F632  gptmr_check_status                 36      2  Code  Lc  Timer.o
  0x8002F656  gptmr_clear_status                 20      2  Code  Lc  Timer.o
  0x8002F66A  gptmr_start_counter                44      2  Code  Lc  Timer.o
  0x8002F696  dma_check_transfer_status         196      2  Code  Lc  uart_dma.o
  0x8002F75A  uart_write_byte                    24      2  Code  Lc  Uart_Irq.o
  0x8002F772  uart_disable_irq                   28      2  Code  Lc  Uart_Irq.o
  0x8002F78E  uart_enable_irq                    24      2  Code  Lc  Uart_Irq.o
  0x8002F7A6  uart_get_irq_id                    24      2  Code  Lc  Uart_Irq.o
  0x8002F7BE  UartIrqInit                       296      2  Code  Gb  Uart_Irq.o
  0x8002F8C8  ANNCompen_Init                    108      2  Code  Gb  AnnTempCompen.o
  0x8002F91C  ComputeGyroTempDiff               880      2  Code  Gb  compen.o
  0x8002FC64  Drv_FlashErase                     60      2  Code  Gb  FirmwareUpdateFile.o
  0x8002FC9C  Drv_FlashWrite                     68      2  Code  Gb  FirmwareUpdateFile.o
  0x8002FCDC  Drv_FlashRead                      32      2  Code  Gb  FirmwareUpdateFile.o
  0x8002FCF8  TaskMange                         244      2  Code  Lc  main.o
  0x8002FDCE  Spi2Task                          100      2  Code  Lc  main.o
  0x8002FE24  Vec_Cross                         316      2  Code  Gb  matvecmath.o
  0x8002FF3C  Mat_Mul                           228      2  Code  Gb  matvecmath.o
  0x80030018  Relu                               80      2  Code  Gb  matvecmath.o
  0x80030064  GetSmi240Data                     108      2  Code  Gb  protocol.o
  0x800300D0  Smi240DataToAlgorithm             800      2  Code  Gb  protocol.o
  0x800303D8  Smi240Spi2Send                    216      2  Code  Gb  protocol.o
  0x800304A4  CombinationUartSend               816      2  Code  Gb  protocol.o
  0x80030760  PureUartSend                    1 296      2  Code  Gb  protocol.o
  0x80030C5C  crc_verify_8bit                    56      2  Code  Lc  SetParaBao.o
  0x80030C94  SendPara_SetHead                  140      2  Code  Gb  SetParaBao.o
  0x80030D20  SendPara_SetEnd                   112      2  Code  Gb  SetParaBao.o
  0x80030D82  UpdateStart_SetHead               140      2  Code  Gb  SetParaBao.o
  0x80030E0E  UpdateStart_SetEnd                112      2  Code  Gb  SetParaBao.o
  0x80030E70  UpdateSend_SetHead                140      2  Code  Gb  SetParaBao.o
  0x80030EFC  UpdateSend_SetEnd                 112      2  Code  Gb  SetParaBao.o
  0x80030F5E  UpdateEnd_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80030FEA  UpdateEnd_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x8003104C  UpdateStop_SetHead                140      2  Code  Gb  SetParaBao.o
  0x800310D8  UpdateStop_SetEnd                 112      2  Code  Gb  SetParaBao.o
  0x8003113A  ReadPara0_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x800311C6  ReadPara0_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x80031228  ReadPara1_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x800312B4  ReadPara1_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x80031316  ReadPara2_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x800313A2  ReadPara2_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x80031404  ReadPara3_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80031490  ReadPara3_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800314F4  ReadPara4_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80031580  ReadPara4_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800315E4  SetParaCoord                      256      2  Code  Gb  SetParaBao.o
  0x800316BC  ReadParaFromFlash                 356      2  Code  Gb  SetParaBao.o
  0x800317E4  ReadPara_1                        308      2  Code  Gb  SetParaBao.o
  0x800318DC  ReadPara_4                        368      2  Code  Gb  SetParaBao.o
  0x80031A10  ReadPara                          268      2  Code  Gb  SetParaBao.o
  0x80031AFC  SetParaGpsType                    240      2  Code  Gb  SetParaBao.o
  0x80031BC8  SetParaDataOutType                240      2  Code  Gb  SetParaBao.o
  0x80031C94  SetParaDebugMode                  240      2  Code  Gb  SetParaBao.o
  0x80031D60  SetParaGyroType                   240      2  Code  Gb  SetParaBao.o
  0x80031E2C  SetParaFactorGyro                 392      2  Code  Gb  SetParaBao.o
  0x80031F88  SetParaFactorAcc                  392      2  Code  Gb  SetParaBao.o
  0x800320E4  ParaUpdateHandle                  400      2  Code  Gb  SetParaBao.o
  0x80032260  SetParaUpdateStop                 232      2  Code  Gb  SetParaBao.o
  0x80032328  TemperCompenAccNerve_X1           280      2  Code  Gb  SetParaBao.o
  0x8003240E  SetParaTemperCompen               412      2  Code  Gb  SetParaBao.o
  0x80032530  Smi980_Init                       152      2  Code  Gb  Smi980.o
  0x800325A0  Smi980_ReadData                   436      2  Code  Gb  Smi980.o
  0x80032718  sysctl_resource_any_is_busy
                                                 28      2  Code  Lc  board.c.o
  0x80032734  sysctl_resource_target_set_mode
                                                 64      2  Code  Lc  board.c.o
  0x80032774  gptmr_check_status                 36      2  Code  Lc  board.c.o
  0x80032798  gptmr_clear_status                 20      2  Code  Lc  board.c.o
  0x800327AC  usb_phy_disable_dp_dm_pulldown
                                                 32      2  Code  Lc  board.c.o
  0x800327CC  pllctlv2_xtal_is_stable            28      2  Code  Lc  board.c.o
  0x800327E8  pllctlv2_xtal_is_enabled           28      2  Code  Lc  board.c.o
  0x80032804  board_init_console                116      2  Code  Gb  board.c.o
  0x80032862  board_init                         68      2  Code  Gb  board.c.o
  0x80032882  board_init_clock                1 112      2  Code  Gb  board.c.o
  0x80032BDE  board_delay_ms                     24      2  Code  Gb  board.c.o
  0x80032BF2  board_init_spi_clock               64      2  Code  Gb  board.c.o
  0x80032C2A  board_init_spi_pins                24      2  Code  Gb  board.c.o
  0x80032C3C  board_init_pmp                      4      2  Code  Gb  board.c.o
  0x80032C40  board_init_uart_clock             288      2  Code  Gb  board.c.o
  0x80032D30  init_py_pins_as_pgpio              76      2  Code  Gb  pinmux.c.o
  0x80032D7C  init_spi_pins                     116      2  Code  Gb  pinmux.c.o
  0x80032DF0  console_init                      112      2  Code  Gb  hpm_debug_console.c.o
  0x80032E56  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  0x80032ED4  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  0x80032EE0  __SEGGER_RTL_X_file_bufsize
                                                 12      2  Code  Gb  hpm_debug_console.c.o
  0x80032EEC  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  0x80032F34  pllctlv2_init_pll_with_freq
                                                184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x80032FEC  pllctlv2_get_pll_freq_in_hz
                                                248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x800330C4  spi_get_data_length_in_bits
                                                 32      2  Code  Lc  hpm_spi_drv.c.o
  0x800330E4  spi_wait_for_idle_status           64      2  Code  Gb  hpm_spi_drv.c.o
  0x80033124  spi_write_address                  52      2  Code  Gb  hpm_spi_drv.c.o
  0x80033158  spi_master_get_default_format_config
                                                 68      2  Code  Gb  hpm_spi_drv.c.o
  0x8003319C  spi_slave_get_default_format_config
                                                 60      2  Code  Gb  hpm_spi_drv.c.o
  0x800331D8  spi_format_init                   128      2  Code  Gb  hpm_spi_drv.c.o
  0x80033258  spi_control_init                  284      2  Code  Gb  hpm_spi_drv.c.o
  0x80033374  spi_transfer                      460      2  Code  Gb  hpm_spi_drv.c.o
  0x8003350A  uart_modem_config                  60      2  Code  Lc  hpm_uart_drv.c.o
  0x80033546  uart_disable_irq                   28      2  Code  Lc  hpm_uart_drv.c.o
  0x80033562  uart_enable_irq                    24      2  Code  Lc  hpm_uart_drv.c.o
  0x8003357A  uart_default_config               148      2  Code  Gb  hpm_uart_drv.c.o
  0x8003360E  uart_flush                         64      2  Code  Gb  hpm_uart_drv.c.o
  0x8003364E  uart_init_rxline_idle_detection
                                                104      2  Code  Gb  hpm_uart_drv.c.o
  0x800336AA  reset_handler                      32      2  Code  Wk  reset.c.o
  0x800336C2  _init                               4      2  Code  Wk  reset.c.o
  0x800336C6  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  0x800336CA  swi_isr                             4      2  Code  Wk  trap.c.o
  0x800336CE  exception_handler                  44      2  Code  Wk  trap.c.o
  0x800336FA  clock_get_frequency               200      2  Code  Gb  hpm_clock_drv.c.o
  0x8003379C  get_frequency_for_dac             156      2  Code  Lc  hpm_clock_drv.c.o
  0x8003382E  get_frequency_for_pewdg            40      2  Code  Lc  hpm_clock_drv.c.o
  0x80033856  get_frequency_for_ahb              48      2  Code  Lc  hpm_clock_drv.c.o
  0x80033882  clock_check_in_group               52      2  Code  Gb  hpm_clock_drv.c.o
  0x800338B0  clock_connect_group_to_cpu
                                                 40      2  Code  Gb  hpm_clock_drv.c.o
  0x800338D8  clock_cpu_delay_ms                224      2  Code  Gb  hpm_clock_drv.c.o
  0x800339B0  clock_update_core_clock            36      2  Code  Gb  hpm_clock_drv.c.o
  0x800339CE  l1c_op                            180      2  Code  Lc  hpm_l1c_drv.c.o
  0x80033A82  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.c.o
  0x80033A9A  sysctl_enable_group_resource
                                                200      2  Code  Gb  hpm_sysctl_drv.c.o
  0x80033B5E  sysctl_check_group_resource_enable
                                                116      2  Code  Gb  hpm_sysctl_drv.c.o
  0x80033BD2  sysctl_config_cpu0_domain_clock
                                                188      2  Code  Gb  hpm_sysctl_drv.c.o
  0x80033C86  enable_plic_feature                44      2  Code  Gb  system.c.o
  0x80033CB2  __SEGGER_RTL_puts_no_nl            44      2  Code  Lc  execops.o (libc_rv32imac_balanced.a)
  0x80033CD6  signal                             52      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80033D0A  raise                             108      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80033D66  abort                              16      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  0x80033D70  __SEGGER_RTL_X_assert             112      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  0x80033DB2  putchar                            16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x80033DBE  puts                               68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x80033DF4  __adddf3                          728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x800340CC  __mulsf3                          176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003417C  __muldf3                          272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003428C  __divsf3                          260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034390  __divdf3                          448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034550  __nesf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034550  __eqsf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003457C  __fixdfsi                          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x800345CC  __fixunssfdi                       96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003462C  __floatunsidf                      72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034674  __SEGGER_RTL_SquareHi_U64          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80034698  __SEGGER_RTL_float64_PolyEvalQ
                                                104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x800346F2  __trunctfsf2                       44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80034716  __SEGGER_RTL_float32_signbit
                                                  4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003471A  ldexpf.localalias                  68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8003471A  ldexpf                             68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003475E  frexpf                             44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003478A  fmodf                             260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003488E  sin                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80034892  cos                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80034896  tan                               488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80034A12  __SEGGER_RTL_float64_asinacos_fpu
                                                528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80034BCA  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034C32  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034C9A  strnlen                           152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  0x80034D32  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  0x80034D3E  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80034DDA  __SEGGER_RTL_print_padding
                                                 48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80034E04  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80034E7A  __SEGGER_RTL_vfprintf_short_float_long
                                              3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  0x80034E7A  __SEGGER_RTL_vfprintf           3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  0x80035B0E  __SEGGER_RTL_ascii_isctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035B3A  __SEGGER_RTL_ascii_tolower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035B4A  __SEGGER_RTL_ascii_iswctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035B76  __SEGGER_RTL_ascii_towlower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035B86  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035B9A  __SEGGER_RTL_current_locale
                                                 20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800362CC  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  0x800362E0  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)

Function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  Acc_Compen_Para_Init            9 246      2  Code  Gb  compen.o
  Gyro_Compen_Para_Init           9 214      2  Code  Gb  compen.o
  AccANNCompen_X_Init             8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Y_Init             8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Z_Init             8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_X_Init            8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Y_Init            8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Z_Init            8 134      2  Code  Gb  AnnTempCompen.o
  __SEGGER_RTL_vfprintf           3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf_short_float_long
                                  3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  NavDataOutputSet                2 318      2  Code  Gb  arithmetic.o
  ZUPTDetection                   2 014      2  Code  Gb  ZUPT.o
  QToCnb                          1 342      2  Code  Gb  navi.o
  PureUartSend                    1 296      2  Code  Gb  protocol.o
  PureUartSend36B                 1 238      2  Code  Gb  protocol.o
  CnbToQ                          1 224      2  Code  Gb  navi.o
  board_init_clock                1 112      2  Code  Gb  board.c.o
  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  SetParaAll                      1 098      2  Code  Gb  SetParaBao.o
  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  Mat_Inv                         1 050      2  Code  Gb  matvecmath.o
  UartDmaRecSetPara                 998      2  Code  Gb  SetParaBao.o
  ComputeQ                          926      2  Code  Gb  navi.o
  ComputeAccTempDiff                886      2  Code  Gb  compen.o
  analysisRxdata                    882      2  Code  Gb  Uart_Irq.o
  ComputeGyroTempDiff               880      2  Code  Gb  compen.o
  UserTask                          862      2  Code  Lc  main.o
  Qua_Mul                           854      2  Code  Gb  matvecmath.o
  ANN_Predict                       818      2  Code  Gb  AnnTempCompen.o
  CombinationUartSend               816      2  Code  Gb  protocol.o
  CombinationSpi2Send               806      2  Code  Gb  protocol.o
  Smi240DataToAlgorithm             800      2  Code  Gb  protocol.o
  sqrt                              778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  InitialBiasEstimate               772      2  Code  Gb  arithmetic.o
  CombinationUartSend22B            758      2  Code  Gb  protocol.o
  AttiToCnb                         750      2  Code  Gb  navi.o
  PureSpi2Send                      730      2  Code  Gb  protocol.o
  CnbToAtti                         728      2  Code  Gb  navi.o
  __adddf3                          728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  startup_diagnostics               674      2  Code  Lc  main.o
  AlgorithmAct                      634      2  Code  Gb  arithmetic.o
  InertialSysAlignCompute           610      2  Code  Gb  align.o
  RestoreFactory                    610      2  Code  Gb  SetParaBao.o
  SaveParaToFlash                   582      2  Code  Gb  SetParaBao.o
  ReadPara_3                        534      2  Code  Gb  SetParaBao.o
  __SEGGER_RTL_float64_asinacos_fpu
                                    528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  uart_calculate_baudrate           526      2  Code  Lc  hpm_uart_drv.c.o
  atan                              510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  AccCompenCompute                  490      2  Code  Gb  compen.o
  tan                               488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  GyroCompenCompute                 474      2  Code  Gb  compen.o
  ApplyBiasCorrectionToCombineData
                                    472      2  Code  Gb  arithmetic.o
  IMUdataPredo                      468      2  Code  Gb  arithmetic.o
  NaviCompute                       460      2  Code  Gb  navi.o
  spi_transfer                      460      2  Code  Gb  hpm_spi_drv.c.o
  uart_init                         454      2  Code  Gb  hpm_uart_drv.c.o
  __divdf3                          448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  ZUPTInit                          446      2  Code  Gb  ZUPT.o
  ComputeG                          444      2  Code  Gb  navi.o
  Smi980_ReadData                   436      2  Code  Gb  Smi980.o
  SetParaGnssInitValue              434      2  Code  Gb  SetParaBao.o
  __addsf3                          430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  RTCompenPara                      422      2  Code  Gb  compen.o
  SetParaTemperCompen               412      2  Code  Gb  SetParaBao.o
  main                              410      2  Code  Gb  main.o
  ParaUpdateHandle                  400      2  Code  Gb  SetParaBao.o
  SetParaFactorAcc                  392      2  Code  Gb  SetParaBao.o
  SetParaFactorGyro                 392      2  Code  Gb  SetParaBao.o
  ReadPara_4                        368      2  Code  Gb  SetParaBao.o
  ReadPara_0                        366      2  Code  Gb  SetParaBao.o
  SetParaUpdateStart                358      2  Code  Gb  SetParaBao.o
  ReadParaFromFlash                 356      2  Code  Gb  SetParaBao.o
  ComputeCen                        354      2  Code  Gb  align.o
  __SEGGER_RTL_float64_cos_inline
                                    354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ComputeCie                        340      2  Code  Gb  align.o
  __SEGGER_RTL_float64_sin_inline
                                    338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  spi_write_read_data               338      2  Code  Gb  hpm_spi_drv.c.o
  ComputeCib0i                      336      2  Code  Gb  align.o
  SetParaUpdateSend                 334      2  Code  Gb  SetParaBao.o
  Vec_Cross                         316      2  Code  Gb  matvecmath.o
  Navi_Init                         314      2  Code  Gb  navi.o
  SetParaAngle                      314      2  Code  Gb  SetParaBao.o
  SetParaDeviation                  314      2  Code  Gb  SetParaBao.o
  SetParaGnss                       314      2  Code  Gb  SetParaBao.o
  SetParaVector                     314      2  Code  Gb  SetParaBao.o
  SpiInitMaster                     314      2  Code  Gb  spi.o
  ComputeRmRn                       312      2  Code  Gb  navi.o
  ReadPara_1                        308      2  Code  Gb  SetParaBao.o
  gptmr_channel_config              306      2  Code  Gb  hpm_gptmr_drv.c.o
  ReadPara_2                        298      2  Code  Gb  SetParaBao.o
  UartIrqInit                       296      2  Code  Gb  Uart_Irq.o
  SetParaUpdateEnd                  290      2  Code  Gb  SetParaBao.o
  board_init_uart_clock             288      2  Code  Gb  board.c.o
  spi_control_init                  284      2  Code  Gb  hpm_spi_drv.c.o
  TemperCompenAccAll_3              282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X2           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y1           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y2           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z1           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z2           282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_3             282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X2          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y2          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z2          282      2  Code  Gb  SetParaBao.o
  board_init_usb_dp_dm_pins         282      2  Code  Gb  board.c.o
  TemperCompenAccNerve_X1           280      2  Code  Gb  SetParaBao.o
  SetParaCalibration                278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_0              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_1              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_2              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccNormal             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_0             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_1             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_2             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNormal            278      2  Code  Gb  SetParaBao.o
  SetParaBaud                       274      2  Code  Gb  SetParaBao.o
  __muldf3                          272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  clock_set_source_divider          270      2  Code  Gb  hpm_clock_drv.c.o
  ReadPara                          268      2  Code  Gb  SetParaBao.o
  ComputeVi                         266      2  Code  Gb  align.o
  __divsf3                          260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  fmodf                             260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  SetParaCoord                      256      2  Code  Gb  SetParaBao.o
  SetParaFrequency                  254      2  Code  Gb  SetParaBao.o
  SetParaTime                       254      2  Code  Gb  SetParaBao.o
  SetParaFilter                     250      2  Code  Gb  SetParaBao.o
  SetParaKalmanQ                    250      2  Code  Gb  SetParaBao.o
  SetParaKalmanR                    250      2  Code  Gb  SetParaBao.o
  SpiInitSlave                      250      2  Code  Gb  spi.o
  pllctlv2_get_pll_freq_in_hz
                                    248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  TaskMange                         244      2  Code  Lc  main.o
  Sys_Init                          242      2  Code  Gb  navi.o
  norflash_verify_config_option
                                    242      2  Code  Gb  flash.o
  spi_read_data                     242      2  Code  Gb  hpm_spi_drv.c.o
  SetParaDataOutType                240      2  Code  Gb  SetParaBao.o
  SetParaDebugMode                  240      2  Code  Gb  SetParaBao.o
  SetParaGpsType                    240      2  Code  Gb  SetParaBao.o
  SetParaGyroType                   240      2  Code  Gb  SetParaBao.o
  ComputeWnbb                       238      2  Code  Gb  navi.o
  Timer_Init                        238      2  Code  Gb  Timer.o
  FinishInertialSysAlign            236      2  Code  Gb  align.o
  SetParaUpdateStop                 232      2  Code  Gb  SetParaBao.o
  ComputeDelSenbb                   230      2  Code  Gb  navi.o
  Mat_Mul                           228      2  Code  Gb  matvecmath.o
  Smi240UartSend                    226      2  Code  Gb  protocol.o
  clock_cpu_delay_ms                224      2  Code  Gb  hpm_clock_drv.c.o
  InertialSysAlign_Init             222      2  Code  Gb  align.o
  LinerCompen_60_ANN_Order          222      2  Code  Gb  compen.o
  board_print_clock_freq            222      2  Code  Gb  board.c.o
  pllctlv2_get_pll_postdiv_freq_in_hz
                                    222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  Smi240Spi2Send                    216      2  Code  Gb  protocol.o
  norflash_diagnose_config_locations
                                    214      2  Code  Gb  flash.o
  norflash_restore_config_option
                                    214      2  Code  Gb  flash.o
  get_frequency_for_source          206      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_frequency               200      2  Code  Gb  hpm_clock_drv.c.o
  sysctl_enable_group_resource
                                    200      2  Code  Gb  hpm_sysctl_drv.c.o
  spi_write_data                    198      2  Code  Gb  hpm_spi_drv.c.o
  dma_check_transfer_status         196      2  Code  Lc  uart_dma.o
  GetTempRangeNum                   194      2  Code  Gb  compen.o
  ComputeWenn                       192      2  Code  Gb  navi.o
  sysctl_config_cpu0_domain_clock
                                    188      2  Code  Gb  hpm_sysctl_drv.c.o
  pllctlv2_init_pll_with_freq
                                    184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  Smi980SpiTransfer                 180      2  Code  Gb  spi.o
  l1c_op                            180      2  Code  Lc  hpm_l1c_drv.c.o
  __mulsf3                          176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  SysVarDefaultSet                  174      2  Code  Gb  navi.o
  __floatundisf                     170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  _clean_up                         170      2  Code  Wk  reset.c.o
  ComputeVib0                       166      2  Code  Gb  align.o
  get_frequency_for_adc             162      2  Code  Lc  hpm_clock_drv.c.o
  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  spi_master_timing_init            158      2  Code  Gb  hpm_spi_drv.c.o
  get_frequency_for_dac             156      2  Code  Lc  hpm_clock_drv.c.o
  Smi980_Init                       152      2  Code  Gb  Smi980.o
  strnlen                           152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  uart_default_config               148      2  Code  Gb  hpm_uart_drv.c.o
  _start                            142      2  Code  Gb  startup.s.o
  sysctl_config_clock               142      2  Code  Gb  hpm_sysctl_drv.c.o
  ComputeWien                       140      2  Code  Gb  navi.o
  ReadPara0_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara1_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara2_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara3_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara4_SetHead                 140      2  Code  Gb  SetParaBao.o
  SendPara_SetHead                  140      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetHead                 140      2  Code  Gb  SetParaBao.o
  UpdateSend_SetHead                140      2  Code  Gb  SetParaBao.o
  UpdateStart_SetHead               140      2  Code  Gb  SetParaBao.o
  UpdateStop_SetHead                140      2  Code  Gb  SetParaBao.o
  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  init_uart_pins                    138      2  Code  Gb  pinmux.c.o
  __truncdfsf2                      134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  MultiDim_Vec_Dot                  130      2  Code  Gb  matvecmath.o
  spi_format_init                   128      2  Code  Gb  hpm_spi_drv.c.o
  __SEGGER_RTL_ldouble_to_double
                                    122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  board_init_console                116      2  Code  Gb  board.c.o
  init_spi_pins                     116      2  Code  Gb  pinmux.c.o
  sysctl_check_group_resource_enable
                                    116      2  Code  Gb  hpm_sysctl_drv.c.o
  get_frequency_for_ip_in_common_group
                                    114      2  Code  Lc  hpm_clock_drv.c.o
  gptmr_channel_get_default_config
                                    114      2  Code  Gb  hpm_gptmr_drv.c.o
  pllctlv2_set_postdiv              114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  ReadPara0_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara1_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara2_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara3_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara4_SetEnd                  112      2  Code  Gb  SetParaBao.o
  SendPara_SetEnd                   112      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetEnd                  112      2  Code  Gb  SetParaBao.o
  UpdateSend_SetEnd                 112      2  Code  Gb  SetParaBao.o
  UpdateStart_SetEnd                112      2  Code  Gb  SetParaBao.o
  UpdateStop_SetEnd                 112      2  Code  Gb  SetParaBao.o
  __SEGGER_RTL_X_assert             112      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  console_init                      112      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_float64_PolyEvalP
                                    110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ANNCompen_Init                    108      2  Code  Gb  AnnTempCompen.o
  GetSmi240Data                     108      2  Code  Gb  protocol.o
  raise                             108      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_xltoa                106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_xtoa                 106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  floorf                            106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ZUPTAngleConstraint               104      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_float64_PolyEvalQ
                                    104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  uart_init_rxline_idle_detection
                                    104      2  Code  Gb  hpm_uart_drv.c.o
  __floatsisf                       102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  spi_transfer_mode_print           102      2  Code  Gb  spi.o
  IMUdataPredp_algParmCache         100      2  Code  Gb  arithmetic.o
  Spi2Task                          100      2  Code  Lc  main.o
  __SEGGER_RTL_pow10f                98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  l1c_dc_invalidate                  98      2  Code  Gb  hpm_l1c_drv.c.o
  __fixunssfdi                       96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  board_print_banner                 94      2  Code  Gb  board.c.o
  system_init                        90      2  Code  Wk  system.c.o
  INS600mAlgorithmEntry              86      2  Code  Gb  arithmetic.o
  Mat_Tr                             86      2  Code  Gb  matvecmath.o
  __floatunsisf                      86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  ldexp                              86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexp.localalias                   86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  norflash_read_mem                  86      2  Code  Gb  flash.o
  UartIrqSendMsg                     82      2  Code  Gb  Uart_Irq.o
  spi_master_get_default_control_config
                                     82      2  Code  Gb  hpm_spi_drv.c.o
  Relu                               80      2  Code  Gb  matvecmath.o
  __fixdfsi                          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsidf                        78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  norflash_init                      78      2  Code  Gb  flash.o
  uart_send_byte                     78      2  Code  Gb  hpm_uart_drv.c.o
  init_py_pins_as_pgpio              76      2  Code  Gb  pinmux.c.o
  __fixsfsi                          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  get_frequency_for_cpu              74      2  Code  Lc  hpm_clock_drv.c.o
  xor_check                          74      2  Code  Gb  protocol.o
  SpiSlaveSend                       72      2  Code  Gb  spi.o
  __floatunsidf                      72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  __extendsfdf2                      70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gedf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ledf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  spi_read_command                   70      2  Code  Gb  hpm_spi_drv.c.o
  Drv_FlashWrite                     68      2  Code  Gb  FirmwareUpdateFile.o
  board_init                         68      2  Code  Gb  board.c.o
  ldexpf                             68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexpf.localalias                  68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  puts                               68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  spi_master_get_default_format_config
                                     68      2  Code  Gb  hpm_spi_drv.c.o
  rom_xpi_nor_program                66      2  Code  Lc  flash.o
  spi_write_command                  66      2  Code  Gb  hpm_spi_drv.c.o
  board_init_spi_clock               64      2  Code  Gb  board.c.o
  spi_wait_for_idle_status           64      2  Code  Gb  hpm_spi_drv.c.o
  sysctl_resource_target_set_mode
                                     64      2  Code  Lc  board.c.o
  uart_flush                         64      2  Code  Gb  hpm_uart_drv.c.o
  __gesf2                            62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  clock_remove_from_group            62      2  Code  Gb  hpm_clock_drv.c.o
  spi_no_data                        62      2  Code  Lc  hpm_spi_drv.c.o
  Drv_FlashErase                     60      2  Code  Gb  FirmwareUpdateFile.o
  rom_xpi_nor_read                   60      2  Code  Lc  flash.o
  spi_slave_get_default_format_config
                                     60      2  Code  Gb  hpm_spi_drv.c.o
  uart_modem_config                  60      2  Code  Lc  hpm_uart_drv.c.o
  Check_16bit                        58      2  Code  Gb  protocol.o
  Check_8bit                         58      2  Code  Gb  protocol.o
  __ltsf2                            58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  get_frequency_for_ewdg             58      2  Code  Lc  hpm_clock_drv.c.o
  norflash_read                      58      2  Code  Gb  flash.o
  norflash_write                     58      2  Code  Gb  flash.o
  rom_xpi_nor_erase_sector           58      2  Code  Lc  flash.o
  spi_get_rx_fifo_valid_data_size
                                     58      2  Code  Lc  hpm_spi_drv.c.o
  spi_slave_get_default_control_config
                                     58      2  Code  Gb  hpm_spi_drv.c.o
  Bind_Init                          56      2  Code  Gb  navi.o
  crc_verify_8bit                    56      2  Code  Lc  SetParaBao.o
  l1c_dc_enable                      54      2  Code  Gb  hpm_l1c_drv.c.o
  clock_check_in_group               52      2  Code  Gb  hpm_clock_drv.c.o
  signal                             52      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  spi_write_address                  52      2  Code  Gb  hpm_spi_drv.c.o
  __fixunsdfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtsf2                            50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  norflash_erase_sector              50      2  Code  Gb  flash.o
  __SEGGER_RTL_print_padding
                                     48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  get_frequency_for_ahb              48      2  Code  Lc  hpm_clock_drv.c.o
  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.c.o
  printf                             46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  sysctl_clock_target_is_busy
                                     46      2  Code  Lc  hpm_sysctl_drv.c.o
  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_isctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_puts_no_nl            44      2  Code  Lc  execops.o (libc_rv32imac_balanced.a)
  __eqsf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __nesf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __trunctfsf2                       44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  enable_plic_feature                44      2  Code  Gb  system.c.o
  exception_handler                  44      2  Code  Wk  trap.c.o
  frexpf                             44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  gptmr_start_counter                44      2  Code  Lc  Timer.o
  rom_xpi_nor_get_property           44      2  Code  Lc  flash.o
  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  fputc                              42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  sysctl_clock_set_preset            42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                                     42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                                     42      2  Code  Lc  hpm_sysctl_drv.c.o
  clock_connect_group_to_cpu
                                     40      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_pewdg            40      2  Code  Lc  hpm_clock_drv.c.o
  norflash_get_chip_size             40      2  Code  Gb  flash.o
  rom_xpi_nor_auto_config            40      2  Code  Lc  flash.o
  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  pllctlv2_xtal_set_rampup_time
                                     38      2  Code  Lc  board.c.o
  sysctl_resource_target_get_mode
                                     38      2  Code  Lc  board.c.o
  __SEGGER_RTL_SquareHi_U64          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  clock_update_core_clock            36      2  Code  Gb  hpm_clock_drv.c.o
  gptmr_check_status                 36      2  Code  Lc  Timer.o
  gptmr_check_status                 36      2  Code  Lc  board.c.o
  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  board_init_uart                    34      2  Code  Gb  board.c.o
  itoa                               34      2  Code  Wk  convops.o (libc_rv32imac_balanced.a)
  spi_get_data_length_in_bytes
                                     34      2  Code  Lc  hpm_spi_drv.c.o
  sysctl_cpu_clock_any_is_busy
                                     34      2  Code  Lc  hpm_sysctl_drv.c.o
  uart_check_status                  34      2  Code  Lc  Uart_Irq.o
  Drv_FlashRead                      32      2  Code  Gb  FirmwareUpdateFile.o
  reset_handler                      32      2  Code  Wk  reset.c.o
  spi_get_data_length_in_bits
                                     32      2  Code  Lc  hpm_spi_drv.c.o
  usb_phy_disable_dp_dm_pulldown
                                     32      2  Code  Lc  board.c.o
  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  gptmr_enable_irq                   28      2  Code  Lc  Timer.o
  pllctlv2_xtal_is_enabled           28      2  Code  Lc  board.c.o
  pllctlv2_xtal_is_stable            28      2  Code  Lc  board.c.o
  sysctl_resource_any_is_busy
                                     28      2  Code  Lc  board.c.o
  uart_disable_irq                   28      2  Code  Lc  Uart_Irq.o
  uart_disable_irq                   28      2  Code  Lc  hpm_uart_drv.c.o
  Drv_SystemReset                    26      2  Code  Gb  FirmwareUpdateFile.o
  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  spi_master_get_default_timing_config
                                     26      2  Code  Gb  hpm_spi_drv.c.o
  board_delay_ms                     24      2  Code  Gb  board.c.o
  board_init_spi_pins                24      2  Code  Gb  board.c.o
  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.c.o
  uart_enable_irq                    24      2  Code  Lc  Uart_Irq.o
  uart_enable_irq                    24      2  Code  Lc  hpm_uart_drv.c.o
  uart_get_irq_id                    24      2  Code  Lc  Uart_Irq.o
  uart_write_byte                    24      2  Code  Lc  Uart_Irq.o
  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  AlgorithmDo                        20      2  Code  Gb  arithmetic.o
  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_current_locale
                                     20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  gptmr_clear_status                 20      2  Code  Lc  Timer.o
  gptmr_clear_status                 20      2  Code  Lc  board.c.o
  __SEGGER_RTL_float32_isnan
                                     18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnormal
                                     18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ppor_sw_reset                      18      2  Code  Lc  FirmwareUpdateFile.o
  syscall_handler                    18      2  Code  Wk  trap.c.o
  uart_read_byte                     18      2  Code  Lc  Uart_Irq.o
  GetZUPTFlag                        16      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_ascii_tolower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towlower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  abort                              16      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  putchar                            16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_toupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isinf
                                     14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __subdf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subsf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                                     12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  abs                                10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  asin                               10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  cos                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  sin                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_signbit
                                      4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  _init                               4      2  Code  Wk  reset.c.o
  board_init_pmp                      4      2  Code  Gb  board.c.o
  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  swi_isr                             4      2  Code  Wk  trap.c.o
  __SEGGER_RTL_SIGNAL_SIG_DFL
                                      2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_ERR
                                      2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_IGN
                                      2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  exit                                2      2  Code  Gb  startup.s.o
  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  start                                      2  Code  Gb  startup.s.o

Read-write data symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  Acc                        0x00089208  gp+0x0660          24      8  Zero  Lc  arithmetic.o
  ImuData                    0x0008AEE8                     28      4  Zero  Gb  main.o
  InavOutData                0x0008AE5C                     80      4  Zero  Gb  arithmetic.o
  LastAcc                    0x000891F0  gp+0x0648          24      8  Zero  Lc  arithmetic.o
  TimeStamp                  0x0008AF60                      4      4  Zero  Gb  main.o
  __RAL_global_locale        0x00080000                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_aSigTab       0x0008AF04                     24      4  Zero  Lc  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_global_locale
                             0x00080000                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_heap_globals  0x0008AF5C                      4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_locale_ptr    0x0008AF58                      4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stdout_file   0x0008AF54                      4      4  Zero  Lc  hpm_debug_console.c.o
  acc_sum                    0x000891D8  gp+0x0630          24      8  Zero  Lc  arithmetic.o
  bias_estimate_done         0x0008AF50                      4      4  Zero  Lc  arithmetic.o
  bias_sample_count          0x0008AF4C                      4      4  Zero  Lc  arithmetic.o
  combineData                0x0008AEAC                     32      4  Zero  Gb  arithmetic.o
  control_Slave_config       0x0008AF28                     12      4  Zero  Gb  spi.o
  control_config             0x0008AF1C                     12      4  Zero  Gb  spi.o
  estimated_acc_bias         0x000891C0  gp+0x0618          24      8  Zero  Lc  arithmetic.o
  estimated_gyro_bias        0x000891A8  gp+0x0600          24      8  Zero  Lc  arithmetic.o
  flag.2                     0x0008AF48                      4      4  Zero  Lc  SetParaBao.o
  fpga_syn                   0x0008AF6A                      1         Zero  Gb  main.o
  g_AccANNCompen             0x00087CA0                  1 800      8  Zero  Gb  main.o
  g_Align                    0x00088FD8  gp+0x0430         184      8  Zero  Gb  main.o
  g_CmdFullTempCompenData    0x00086E18                  1 920      8  Zero  Gb  main.o
  g_CmdNormalTempCompenData  0x00088E78  gp+0x02D0         352      8  Zero  Gb  main.o
  g_Compen                   0x00083B30                 10 200      8  Zero  Gb  main.o
  g_GyroANNCompen            0x00087598                  1 800      8  Zero  Gb  main.o
  g_InertialSysAlign         0x00088978  gp-0x0230         832      8  Zero  Gb  main.o
  g_InitBind                 0x00089110  gp+0x0568          80      8  Zero  Gb  main.o
  g_Kalman                   0x00080018                 15 128      8  Zero  Gb  main.o
  g_Navi                     0x00086308                  2 832      8  Zero  Gb  main.o
  g_SelfTest                 0x00089090  gp+0x04E8         128      8  Zero  Gb  main.o
  g_StartUpdateFirm          0x0008AF69                      1         Zero  Gb  SetParaBao.o
  g_SysVar                   0x00088CB8  gp+0x0110         448      8  Zero  Gb  main.o
  g_UpdateBackFlag           0x0008AF7C                      1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful         0x0008AF68                      1         Zero  Gb  SetParaBao.o
  g_ZUPT                     0x000883A8  gp-0x0800       1 488      8  Zero  Gb  main.o
  g_console_uart             0x0008AF44                      4      4  Zero  Lc  hpm_debug_console.c.o
  g_ucSystemResetFlag        0x0008AF67                      1         Zero  Gb  SetParaBao.o
  gbtxcompleted              0x0008AF78                      4      4  Init  Gb  Uart_Irq.o
  gframeParsebuf             0x0008A95C                  1 024      4  Zero  Gb  Uart_Irq.o
  grxbuffer                  0x0008AF80                  4 096      4  None  Gb  Uart_Irq.o
  grxlen                     0x0008AF40                      4      4  Zero  Gb  Uart_Irq.o
  grxst                      0x0008AF3C                      4      4  Zero  Gb  Uart_Irq.o
  gyro_sum                   0x00089190  gp+0x05E8          24      8  Zero  Lc  arithmetic.o
  hpm_core_clock             0x0008AF38                      4      4  Zero  Gb  hpm_clock_drv.c.o
  nbr_data_to_send           0x0008AF74                      4      4  Init  Gb  Uart_Irq.o
  r_Gyro                     0x00089178  gp+0x05D0          24      8  Zero  Lc  arithmetic.o
  r_LastGyro                 0x00089160  gp+0x05B8          24      8  Zero  Lc  arithmetic.o
  s_xpi_nor_config           0x0008AD5C                    256      4  Zero  Lc  flash.o
  stSetPara                  0x00089220  gp+0x0678       5 946      4  Zero  Gb  SetParaBao.o
  stSmi240Data               0x0008AECC                     28      4  Zero  Gb  protocol.o
  stdout                     0x0008AF70                      4      4  Init  Gb  hpm_debug_console.c.o
  tCnt.0                     0x0008AF64                      2      2  Zero  Lc  main.o
  timer_cb                   0x0008AF34                      4      4  Zero  Lc  board.c.o
  tx_buffer                  0x0008BF80                  4 096      4  None  Gb  Uart_Irq.o
  tx_counter                 0x0008A95A                      2      2  Zero  Gb  Uart_Irq.o
  uart_rx_dma_done           0x0008AF66                      1         Zero  Gb  uart_dma.o
  uart_tx_dma_done           0x0008AF6B                      1         Init  Gb  uart_dma.o
  uiLastBaoInDex.3           0x0008AF6C                      4      4  Init  Lc  SetParaBao.o
  uiOffsetAddr.1             0x00080014                      4      4  Zero  Lc  SetParaBao.o

Read-write data symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00080000             __SEGGER_RTL_global_locale
                                                            20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x00080000             __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x00080014             uiOffsetAddr.1                      4      4  Zero  Lc  SetParaBao.o
  0x00080018             g_Kalman                       15 128      8  Zero  Gb  main.o
  0x00083B30             g_Compen                       10 200      8  Zero  Gb  main.o
  0x00086308             g_Navi                          2 832      8  Zero  Gb  main.o
  0x00086E18             g_CmdFullTempCompenData         1 920      8  Zero  Gb  main.o
  0x00087598             g_GyroANNCompen                 1 800      8  Zero  Gb  main.o
  0x00087CA0             g_AccANNCompen                  1 800      8  Zero  Gb  main.o
  0x000883A8  gp-0x0800  g_ZUPT                          1 488      8  Zero  Gb  main.o
  0x00088978  gp-0x0230  g_InertialSysAlign                832      8  Zero  Gb  main.o
  0x00088CB8  gp+0x0110  g_SysVar                          448      8  Zero  Gb  main.o
  0x00088E78  gp+0x02D0  g_CmdNormalTempCompenData         352      8  Zero  Gb  main.o
  0x00088FD8  gp+0x0430  g_Align                           184      8  Zero  Gb  main.o
  0x00089090  gp+0x04E8  g_SelfTest                        128      8  Zero  Gb  main.o
  0x00089110  gp+0x0568  g_InitBind                         80      8  Zero  Gb  main.o
  0x00089160  gp+0x05B8  r_LastGyro                         24      8  Zero  Lc  arithmetic.o
  0x00089178  gp+0x05D0  r_Gyro                             24      8  Zero  Lc  arithmetic.o
  0x00089190  gp+0x05E8  gyro_sum                           24      8  Zero  Lc  arithmetic.o
  0x000891A8  gp+0x0600  estimated_gyro_bias                24      8  Zero  Lc  arithmetic.o
  0x000891C0  gp+0x0618  estimated_acc_bias                 24      8  Zero  Lc  arithmetic.o
  0x000891D8  gp+0x0630  acc_sum                            24      8  Zero  Lc  arithmetic.o
  0x000891F0  gp+0x0648  LastAcc                            24      8  Zero  Lc  arithmetic.o
  0x00089208  gp+0x0660  Acc                                24      8  Zero  Lc  arithmetic.o
  0x00089220  gp+0x0678  stSetPara                       5 946      4  Zero  Gb  SetParaBao.o
  0x0008A95A             tx_counter                          2      2  Zero  Gb  Uart_Irq.o
  0x0008A95C             gframeParsebuf                  1 024      4  Zero  Gb  Uart_Irq.o
  0x0008AD5C             s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  0x0008AE5C             InavOutData                        80      4  Zero  Gb  arithmetic.o
  0x0008AEAC             combineData                        32      4  Zero  Gb  arithmetic.o
  0x0008AECC             stSmi240Data                       28      4  Zero  Gb  protocol.o
  0x0008AEE8             ImuData                            28      4  Zero  Gb  main.o
  0x0008AF04             __SEGGER_RTL_aSigTab               24      4  Zero  Lc  execops.o (libc_rv32imac_balanced.a)
  0x0008AF1C             control_config                     12      4  Zero  Gb  spi.o
  0x0008AF28             control_Slave_config               12      4  Zero  Gb  spi.o
  0x0008AF34             timer_cb                            4      4  Zero  Lc  board.c.o
  0x0008AF38             hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  0x0008AF3C             grxst                               4      4  Zero  Gb  Uart_Irq.o
  0x0008AF40             grxlen                              4      4  Zero  Gb  Uart_Irq.o
  0x0008AF44             g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  0x0008AF48             flag.2                              4      4  Zero  Lc  SetParaBao.o
  0x0008AF4C             bias_sample_count                   4      4  Zero  Lc  arithmetic.o
  0x0008AF50             bias_estimate_done                  4      4  Zero  Lc  arithmetic.o
  0x0008AF54             __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  0x0008AF58             __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x0008AF5C             __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0x0008AF60             TimeStamp                           4      4  Zero  Gb  main.o
  0x0008AF64             tCnt.0                              2      2  Zero  Lc  main.o
  0x0008AF66             uart_rx_dma_done                    1         Zero  Gb  uart_dma.o
  0x0008AF67             g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  0x0008AF68             g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  0x0008AF69             g_StartUpdateFirm                   1         Zero  Gb  SetParaBao.o
  0x0008AF6A             fpga_syn                            1         Zero  Gb  main.o
  0x0008AF6B             uart_tx_dma_done                    1         Init  Gb  uart_dma.o
  0x0008AF6C             uiLastBaoInDex.3                    4      4  Init  Lc  SetParaBao.o
  0x0008AF70             stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  0x0008AF74             nbr_data_to_send                    4      4  Init  Gb  Uart_Irq.o
  0x0008AF78             gbtxcompleted                       4      4  Init  Gb  Uart_Irq.o
  0x0008AF7C             g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  0x0008AF80             grxbuffer                       4 096      4  None  Gb  Uart_Irq.o
  0x0008BF80             tx_buffer                       4 096      4  None  Gb  Uart_Irq.o

Read-write data symbols by descending size:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  g_Kalman                       15 128      8  Zero  Gb  main.o
  g_Compen                       10 200      8  Zero  Gb  main.o
  stSetPara                       5 946      4  Zero  Gb  SetParaBao.o
  grxbuffer                       4 096      4  None  Gb  Uart_Irq.o
  tx_buffer                       4 096      4  None  Gb  Uart_Irq.o
  g_Navi                          2 832      8  Zero  Gb  main.o
  g_CmdFullTempCompenData         1 920      8  Zero  Gb  main.o
  g_AccANNCompen                  1 800      8  Zero  Gb  main.o
  g_GyroANNCompen                 1 800      8  Zero  Gb  main.o
  g_ZUPT                          1 488      8  Zero  Gb  main.o
  gframeParsebuf                  1 024      4  Zero  Gb  Uart_Irq.o
  g_InertialSysAlign                832      8  Zero  Gb  main.o
  g_SysVar                          448      8  Zero  Gb  main.o
  g_CmdNormalTempCompenData         352      8  Zero  Gb  main.o
  s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  g_Align                           184      8  Zero  Gb  main.o
  g_SelfTest                        128      8  Zero  Gb  main.o
  InavOutData                        80      4  Zero  Gb  arithmetic.o
  g_InitBind                         80      8  Zero  Gb  main.o
  combineData                        32      4  Zero  Gb  arithmetic.o
  ImuData                            28      4  Zero  Gb  main.o
  stSmi240Data                       28      4  Zero  Gb  protocol.o
  Acc                                24      8  Zero  Lc  arithmetic.o
  LastAcc                            24      8  Zero  Lc  arithmetic.o
  __SEGGER_RTL_aSigTab               24      4  Zero  Lc  execops.o (libc_rv32imac_balanced.a)
  acc_sum                            24      8  Zero  Lc  arithmetic.o
  estimated_acc_bias                 24      8  Zero  Lc  arithmetic.o
  estimated_gyro_bias                24      8  Zero  Lc  arithmetic.o
  gyro_sum                           24      8  Zero  Lc  arithmetic.o
  r_Gyro                             24      8  Zero  Lc  arithmetic.o
  r_LastGyro                         24      8  Zero  Lc  arithmetic.o
  __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_global_locale
                                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  control_Slave_config               12      4  Zero  Gb  spi.o
  control_config                     12      4  Zero  Gb  spi.o
  TimeStamp                           4      4  Zero  Gb  main.o
  __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  bias_estimate_done                  4      4  Zero  Lc  arithmetic.o
  bias_sample_count                   4      4  Zero  Lc  arithmetic.o
  flag.2                              4      4  Zero  Lc  SetParaBao.o
  g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  gbtxcompleted                       4      4  Init  Gb  Uart_Irq.o
  grxlen                              4      4  Zero  Gb  Uart_Irq.o
  grxst                               4      4  Zero  Gb  Uart_Irq.o
  hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  nbr_data_to_send                    4      4  Init  Gb  Uart_Irq.o
  stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  timer_cb                            4      4  Zero  Lc  board.c.o
  uiLastBaoInDex.3                    4      4  Init  Lc  SetParaBao.o
  uiOffsetAddr.1                      4      4  Zero  Lc  SetParaBao.o
  tCnt.0                              2      2  Zero  Lc  main.o
  tx_counter                          2      2  Zero  Gb  Uart_Irq.o
  fpga_syn                            1         Zero  Gb  main.o
  g_StartUpdateFirm                   1         Zero  Gb  SetParaBao.o
  g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  uart_rx_dma_done                    1         Zero  Gb  uart_dma.o
  uart_tx_dma_done                    1         Init  Gb  uart_dma.o

Read-only data symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_Moeller_inverse_lut
                             0x800116B0                  1 024      4  Cnst  Lc  intops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_aPower2f      0x80011AB0                     24      4  Cnst  Lc  utilops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_aSqrtData     0x80011530                    384      4  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_ctype_map
                             0x80011C64                    128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_ctype_mask
                             0x80012F88                     13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale      0x80011BE0                     12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_day_names
                             0x80012EBC                     29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_month_names
                             0x80012E00                     49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_am_pm_indicator
                             0x800139A4                      7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_data
                             0x80011BEC                     88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_date_format
                             0x8001319C                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_date_time_format
                             0x80013994                     15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_day_names
                             0x80012B94                     58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_month_names
                             0x800139AC                     87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_time_format
                             0x800130C0                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_codeset_ascii
                             0x80011C44                     32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_data_empty_string
                             0x80012DB8                      1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_data_utf8_period
                             0x80012318                      2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float64_ASinACos
                             0x80010560  tp-0x0330         112      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_ATan  0x800104C8  tp-0x03C8          96      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_SinCos
                             0x800105D0  tp-0x02C0          64      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_Tan   0x80010528  tp-0x0368          56      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_hex_lc        0x80011AC8                     16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_hex_uc        0x80011AD8                     16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ipow10        0x80010610  tp-0x0280         160      8  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_init_data__       0x80035C04                [1 736]      4  Cnst  Lc  [ Linker created ]
  __SEGGER_init_table__      0x80035BB0                   [84]      4  Cnst  Lc  [ Linker created ]
  fw_info                    0x8000E010                    128      4  Cnst  Gb  hpm_bootheader.c.o
  header                     0x8000E000                     16      4  Cnst  Gb  hpm_bootheader.c.o
  option                     0x8000D000                     16      4  Cnst  Gb  board.c.o
  s_adc_clk_mux_node         0x80011E5C                      2      4  Cnst  Lc  hpm_clock_drv.c.o
  s_dac_clk_mux_node         0x80011F34                      2      4  Cnst  Lc  hpm_clock_drv.c.o
  s_wdgs                     0x800113A8                      8      4  Cnst  Lc  hpm_clock_drv.c.o

Read-only data symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x8000D000             option                             16      4  Cnst  Gb  board.c.o
  0x8000E000             header                             16      4  Cnst  Gb  hpm_bootheader.c.o
  0x8000E010             fw_info                           128      4  Cnst  Gb  hpm_bootheader.c.o
  0x800104C8  tp-0x03C8  __SEGGER_RTL_float64_ATan          96      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010528  tp-0x0368  __SEGGER_RTL_float64_Tan           56      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010560  tp-0x0330  __SEGGER_RTL_float64_ASinACos
                                                           112      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x800105D0  tp-0x02C0  __SEGGER_RTL_float64_SinCos
                                                            64      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010610  tp-0x0280  __SEGGER_RTL_ipow10               160      8  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x800113A8             s_wdgs                              8      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80011530             __SEGGER_RTL_aSqrtData            384      4  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x800116B0             __SEGGER_RTL_Moeller_inverse_lut
                                                         1 024      4  Cnst  Lc  intops.o (libc_rv32imac_balanced.a)
  0x80011AB0             __SEGGER_RTL_aPower2f              24      4  Cnst  Lc  utilops.o (libc_rv32imac_balanced.a)
  0x80011AC8             __SEGGER_RTL_hex_lc                16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80011AD8             __SEGGER_RTL_hex_uc                16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80011BE0             __SEGGER_RTL_c_locale              12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011BEC             __SEGGER_RTL_c_locale_data
                                                            88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011C44             __SEGGER_RTL_codeset_ascii
                                                            32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011C64             __SEGGER_RTL_ascii_ctype_map
                                                           128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011E5C             s_adc_clk_mux_node                  2      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80011F34             s_dac_clk_mux_node                  2      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80012318             __SEGGER_RTL_data_utf8_period
                                                             2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012B94             __SEGGER_RTL_c_locale_day_names
                                                            58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012DB8             __SEGGER_RTL_data_empty_string
                                                             1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012E00             __SEGGER_RTL_c_locale_abbrev_month_names
                                                            49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012EBC             __SEGGER_RTL_c_locale_abbrev_day_names
                                                            29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012F88             __SEGGER_RTL_ascii_ctype_mask
                                                            13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800130C0             __SEGGER_RTL_c_locale_time_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8001319C             __SEGGER_RTL_c_locale_date_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80013994             __SEGGER_RTL_c_locale_date_time_format
                                                            15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800139A4             __SEGGER_RTL_c_locale_am_pm_indicator
                                                             7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800139AC             __SEGGER_RTL_c_locale_month_names
                                                            87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035BB0             __SEGGER_init_table__            [84]      4  Cnst  Lc  [ Linker created ]
  0x80035C04             __SEGGER_init_data__          [1 736]      4  Cnst  Lc  [ Linker created ]

Untyped symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
  Symbol name                     Value     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __AHB_SRAM_segment_end__   0xF0408000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_size__  0x00008000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_start__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_end__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_start__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_end__
                             0x80010000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_size__
                             0x00002000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_end__
                             0x8000E090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_size__
                             0x00000090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __DLM_segment_end__        0x000A0000                                ----  Gb  [ Linker created ]
  __DLM_segment_size__       0x00020000                                ----  Gb  [ Linker created ]
  __DLM_segment_start__      0x00080000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_end__   0x000A0000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_size__  0x00020000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_start__
                             0x00080000                                ----  Gb  [ Linker created ]
  __HEAPSIZE__               0x00004000                                ----  Gb  [ Linker created ]
  __ILM_segment_end__        0x00020000                                ----  Gb  [ Linker created ]
  __ILM_segment_size__       0x00020000                                ----  Gb  [ Linker created ]
  __ILM_segment_start__      0x00000000                                ----  Gb  [ Linker created ]
  __ILM_segment_used_end__   0x000006A2                                ----  Gb  [ Linker created ]
  __ILM_segment_used_size__  0x000006A2                                ----  Gb  [ Linker created ]
  __ILM_segment_used_start__
                             0x00000000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_end__
                             0x8000DC00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_size__
                             0x00000C00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_end__
                             0x8000D010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_size__
                             0x00000010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __SEGGER_RTL_fdiv_reciprocal_table
                             0x80011430                    256      4  Cnst  Lc  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __STACKSIZE__              0x00004000                                ----  Gb  [ Linker created ]
  __XPI0_segment_end__       0x80100000                                ----  Gb  [ Linker created ]
  __XPI0_segment_size__      0x000F0000                                ----  Gb  [ Linker created ]
  __XPI0_segment_start__     0x80010000                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_end__  0x800362FC                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_size__
                             0x000262FC                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_start__
                             0x80010000                                ----  Gb  [ Linker created ]
  __app_load_addr__          0x80010000                                ----  Gb  [ Linker created ]
  __app_offset__             0x00002000                                ----  Gb  [ Linker created ]
  __boot_header_length__     0x00000090                                ----  Gb  [ Linker created ]
  __boot_header_load_addr__  0x8000E000                                ----  Gb  [ Linker created ]
  __fsymtab_end              0x80012DB9                                ----  Gb  [ Linker created ]
  __fsymtab_start            0x80012DB9                                ----  Gb  [ Linker created ]
  __fw_size__                0x00001000                                ----  Gb  [ Linker created ]
  __global_pointer$          0x00088BA8  gp+0x0000                     ----  Gb  [ Linker created ]
  __heap_end__               0x00090F80                                ----  Gb  [ Linker created ]
  __heap_start__             0x0008CF80                                ----  Gb  [ Linker created ]
  __nor_cfg_option_load_addr__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __rt_init_end              0x80012DB9                                ----  Gb  [ Linker created ]
  __rt_init_start            0x80012DB9                                ----  Gb  [ Linker created ]
  __rtmsymtab_end            0x80012DB9                                ----  Gb  [ Linker created ]
  __rtmsymtab_start          0x80012DB9                                ----  Gb  [ Linker created ]
  __stack_end__              0x000A0000                                ----  Gb  [ Linker created ]
  __startup_complete         0x80010054                             2  Code  Lc  startup.s.o
  __thread_pointer$          0x80010890  tp+0x0000                     ----  Gb  [ Linker created ]
  __usbh_class_info_end__    0x00080000                                ----  Gb  [ Linker created ]
  __usbh_class_info_start__  0x00080000                                ----  Gb  [ Linker created ]
  __vector_table             0x00000000                  [292]    512  Init  Gb  startup.s.o
  __vsymtab_end              0x80012DB9                                ----  Gb  [ Linker created ]
  __vsymtab_start            0x80012DB9                                ----  Gb  [ Linker created ]
  _flash_size                0x00100000                                ----  Gb  [ Linker created ]
  _stack                     0x000A0000                                ----  Gb  [ Linker created ]
  _stack_safe                0x000A0000                                ----  Gb  [ Linker created ]
  default_irq_handler        0x00000562                             4  Init  Wk  startup.s.o
  default_isr_1              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_10             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_11             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_12             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_13             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_14             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_15             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_16             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_17             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_18             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_19             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_2              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_20             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_21             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_22             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_23             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_24             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_25             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_26             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_27             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_28             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_29             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_3              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_30             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_31             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_32             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_33             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_34             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_35             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_36             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_37             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_38             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_39             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_4              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_40             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_41             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_42             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_43             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_44             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_45             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_46             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_47             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_48             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_49             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_5              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_50             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_51             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_52             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_53             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_54             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_55             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_56             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_57             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_58             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_59             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_6              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_60             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_61             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_62             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_63             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_64             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_65             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_66             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_67             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_68             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_69             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_7              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_70             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_71             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_72             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_8              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_9              0x00000562                             4  Init  Wk  startup.s.o
  nmi_handler                0x00000560                    [6]      4  Init  Wk  startup.s.o

Untyped symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
       Value     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00000000             __vector_table                  [292]    512  Init  Gb  startup.s.o
  0x00000000             __ILM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __ILM_segment_start__                         ----  Gb  [ Linker created ]
  0x00000000             __AHB_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000010             __NOR_CFG_OPTION_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000090             __boot_header_length__                        ----  Gb  [ Linker created ]
  0x00000090             __BOOT_HEADER_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000560             nmi_handler                       [6]      4  Init  Wk  startup.s.o
  0x00000562             default_isr_9                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_8                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_72                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_71                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_70                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_7                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_69                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_68                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_67                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_66                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_65                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_64                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_63                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_62                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_61                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_60                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_6                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_59                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_58                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_57                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_56                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_55                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_54                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_53                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_52                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_51                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_50                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_5                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_49                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_48                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_47                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_46                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_45                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_44                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_43                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_42                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_41                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_40                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_4                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_39                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_38                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_37                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_36                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_35                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_34                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_33                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_32                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_31                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_30                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_3                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_29                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_28                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_27                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_26                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_25                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_24                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_23                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_22                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_21                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_20                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_2                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_19                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_18                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_17                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_16                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_15                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_14                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_13                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_12                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_11                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_10                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_1                              4  Init  Wk  startup.s.o
  0x00000562             default_irq_handler                        4  Init  Wk  startup.s.o
  0x000006A2             __ILM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x000006A2             __ILM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x00000C00             __NOR_CFG_OPTION_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00001000             __fw_size__                                   ----  Gb  [ Linker created ]
  0x00002000             __app_offset__                                ----  Gb  [ Linker created ]
  0x00002000             __BOOT_HEADER_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00004000             __STACKSIZE__                                 ----  Gb  [ Linker created ]
  0x00004000             __HEAPSIZE__                                  ----  Gb  [ Linker created ]
  0x00008000             __AHB_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x00020000             __ILM_segment_size__                          ----  Gb  [ Linker created ]
  0x00020000             __ILM_segment_end__                           ----  Gb  [ Linker created ]
  0x00020000             __DLM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x00020000             __DLM_segment_size__                          ----  Gb  [ Linker created ]
  0x000262FC             __XPI0_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_start__                     ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_end__                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_start__                         ----  Gb  [ Linker created ]
  0x00088BA8  gp+0x0000  __global_pointer$                             ----  Gb  [ Linker created ]
  0x0008CF80             __heap_start__                                ----  Gb  [ Linker created ]
  0x00090F80             __heap_end__                                  ----  Gb  [ Linker created ]
  0x000A0000             _stack_safe                                   ----  Gb  [ Linker created ]
  0x000A0000             _stack                                        ----  Gb  [ Linker created ]
  0x000A0000             __stack_end__                                 ----  Gb  [ Linker created ]
  0x000A0000             __DLM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x000A0000             __DLM_segment_end__                           ----  Gb  [ Linker created ]
  0x000F0000             __XPI0_segment_size__                         ----  Gb  [ Linker created ]
  0x00100000             _flash_size                                   ----  Gb  [ Linker created ]
  0x8000D000             __nor_cfg_option_load_addr__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D010             __NOR_CFG_OPTION_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000DC00             __NOR_CFG_OPTION_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __boot_header_load_addr__                     ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E090             __BOOT_HEADER_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __app_load_addr__                             ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_start__                        ----  Gb  [ Linker created ]
  0x80010000             __BOOT_HEADER_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010054             __startup_complete                         2  Code  Lc  startup.s.o
  0x80010890  tp+0x0000  __thread_pointer$                             ----  Gb  [ Linker created ]
  0x80011430             __SEGGER_RTL_fdiv_reciprocal_table
                                                           256      4  Cnst  Lc  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80012DB9             __vsymtab_start                               ----  Gb  [ Linker created ]
  0x80012DB9             __vsymtab_end                                 ----  Gb  [ Linker created ]
  0x80012DB9             __rtmsymtab_start                             ----  Gb  [ Linker created ]
  0x80012DB9             __rtmsymtab_end                               ----  Gb  [ Linker created ]
  0x80012DB9             __rt_init_start                               ----  Gb  [ Linker created ]
  0x80012DB9             __rt_init_end                                 ----  Gb  [ Linker created ]
  0x80012DB9             __fsymtab_start                               ----  Gb  [ Linker created ]
  0x80012DB9             __fsymtab_end                                 ----  Gb  [ Linker created ]
  0x800362FC             __XPI0_segment_used_end__                     ----  Gb  [ Linker created ]
  0x80100000             __XPI0_segment_end__                          ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0408000             __AHB_SRAM_segment_end__                      ----  Gb  [ Linker created ]


***********************************************************************************************
***                                                                                         ***
***                                      LINK SUMMARY                                       ***
***                                                                                         ***
***********************************************************************************************

Memory breakdown:

  147 072 bytes read-only  code    + 
   11 179 bytes read-only  data    = 158 251 bytes read-only (total)
   85 885 bytes read-write data

Region summary:

  Name        Range                     Size                 Used               Unused       Alignment Loss
  ----------  -----------------  -----------  -------------------  -------------------  -------------------
  ILM         00000000-0001ffff      131 072        1 696   1.29%      129 374  98.70%            2   0.00%
  DLM         00080000-0009ffff      131 072       85 885  65.53%       45 187  34.47%            0   0.00%
  NOR_CFG_OPTION
              8000d000-8000dbff        3 072           16   0.52%        3 056  99.48%            0   0.00%
  BOOT_HEADER
              8000e000-8000ffff        8 192          144   1.76%        8 048  98.24%            0   0.00%
  XPI0        80010000-800fffff      983 040      156 395  15.91%      826 634  84.09%           11   0.00%

Link complete: 0 errors, 0 warnings, 0 remarks
