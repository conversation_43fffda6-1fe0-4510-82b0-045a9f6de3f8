***********************************************************************************************
***                                                                                         ***
***                                    LINK INFORMATION                                     ***
***                                                                                         ***
***********************************************************************************************

Linker version:

  SEGGER RISC-V Linker 4.38.11 compiled Jun 11 2024 14:00:05
  Copyright (c) 2017-2022 SEGGER Microcontroller GmbH    www.segger.com


***********************************************************************************************
***                                                                                         ***
***                                     MODULE SUMMARY                                      ***
***                                                                                         ***
***********************************************************************************************

Memory use by input file:

  Object File                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  align.o                                             2 310          40                        
  AnnTempCompen.o                                    47 674                                    
  arithmetic.o                                        4 282          32                     312
  board.c.o                                           2 356         956                       4
  compen.o                                           20 590          24                        
  FirmwareUpdateFile.o                                  186                                    
  flash.o                                               874         288                     256
  hpm_bootheader.c.o                                                144                        
  hpm_clock_drv.c.o                                   1 776         140                       4
  hpm_debug_console.c.o                                 252                       4           8
  hpm_gptmr_drv.c.o                                     420                                    
  hpm_l1c_drv.c.o                                       390         187                        
  hpm_pcfg_drv.c.o                                       72                                    
  hpm_pllctlv2_drv.c.o                                  700          24                        
  hpm_spi_drv.c.o                                     2 476                                    
  hpm_sysctl_drv.c.o                                    750                                    
  hpm_uart_drv.c.o                                    1 384          24                        
  main.o                                              1 780         968                  37 227
  matvecmath.o                                        2 536           8                        
  navi.o                                              6 626         152                        
  pinmux.c.o                                            330                                    
  protocol.o                                          6 664          32                      28
  reset.c.o                                             198                                    
  SetParaBao.o                                       20 426         332           5       5 957
  Smi980.o                                              488          44                        
  spi.o                                                 790       1 051                      24
  startup.s.o                                           108         292                        
  system.c.o                                            130                                    
  Timer.o                                               536                                    
  trap.c.o                                              384          64                        
  uart_dma.o                                            414                       1           1
  Uart_Irq.o                                          1 856          26           8       9 226
  ZUPT.o                                              2 286         808                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (33 objects)                             132 044       5 636          18      53 047
  ---------------------------------------------  ----------  ----------  ----------  ----------
  heapops_basic_rv32imac_balanced.a                      22                                   4
  libc_rv32imac_balanced.a                           14 480       2 486                      24
  mbops_timeops_rv32imac_balanced.a                     230         549          20           4
  SEGGER_RV32_crtinit_rv32imac_balanced.a                68                                    
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (4 archives)                              14 800       3 035          20          32
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Linker created (shared data, fills, blocks):                    1 944                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            146 844      10 615          38      85 847
  =============================================  ==========  ==========  ==========  ==========

Memory use by archive member:

  Archive member                                    RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
                                                      3 220         248                        
  convops.o (libc_rv32imac_balanced.a)                  134                                    
  execops.o (libc_rv32imac_balanced.a)                  262          30                      24
  fileops.o (libc_rv32imac_balanced.a)                  172                                    
  floatasmops_rv.o (libc_rv32imac_balanced.a)         3 888         256                        
  floatops.o (libc_rv32imac_balanced.a)               3 532         712                        
  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
                                                         22                                   4
  intasmops_rv.o (libc_rv32imac_balanced.a)              38                                    
  intops.o (libc_rv32imac_balanced.a)                 2 150       1 024                        
  mbops.o (mbops_timeops_rv32imac_balanced.a)           230         549          20           4
  prinops.o (libc_rv32imac_balanced.a)                  500         192                        
  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
                                                         68                                    
  strasmops_rv.o (libc_rv32imac_balanced.a)             342                                    
  strops.o (libc_rv32imac_balanced.a)                   152                                    
  utilops.o (libc_rv32imac_balanced.a)                   90          24                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (15 members from 4 archives)              14 800       3 035          20          32
  Objects (33 files)                                132 044       5 636          18      53 047
  Linker created (shared data, fills, blocks):                    1 944                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            146 844      10 615          38      85 847
  =============================================  ==========  ==========  ==========  ==========

Memory use by linker:

  Description                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Initialization table                                            1 820                        
  Memory for block 'heap'                                                                16 384
  Memory for block 'stack'                                                               16 384
  Merged section data (32-bit)                                       28                        
  Merged section data (64-bit)                                       96                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (linker created):                                      1 944                  32 768
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Objects (33 files)                                132 044       5 636          18      53 047
  Archives (4 files)                                 14 800       3 035          20          32
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            146 844      10 615          38      85 847
  =============================================  ==========  ==========  ==========  ==========


***********************************************************************************************
***                                                                                         ***
***                                     SECTION DETAIL                                      ***
***                                                                                         ***
***********************************************************************************************

Sections by address:

  Range              Symbol or [section] Name         Size  Al  Init  Ac  Object File
  -----------------  -------------------------  ----------  --  ----  --  -----------
  00000000-00000123  __vector_table                    292  512
                                                                Init  RO  startup.s.o
  00000124-000001e1  tick_ms_isr                       190   2  Init  RX  Timer.o
  000001e2-000002bb  dma_isr                           218   2  Init  RX  uart_dma.o
  000002bc-000004a1  uart_isr                          486   2  Init  RX  Uart_Irq.o
  000004a2-0000055f  board_timer_isr                   190   2  Init  RX  board.c.o
  00000560-00000565  nmi_handler                         6   4  Init  RX  startup.s.o
  00000566-00000567  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000568-000006a1  irq_handler_trap                  314   4  Init  RX  trap.c.o
  000006a2-000006a3  ( ALIGN .=.+2 )                     2   -  ----  -   -
  000006a4-0007ffff  ( UNUSED .=.+522588 )         522 588   -  ----  -   -
  00080000-00080013  __SEGGER_RTL_global_locale
                                                        20   4  Init  RW  mbops.o (mbops_timeops_rv32imac_balanced.a)
  00080014-00080017  uiOffsetAddr.1                      4   4  Zero  ZI  SetParaBao.o
  00080018-00083b2f  g_Kalman                       15 128   8  Zero  ZI  main.o
  00083b30-00086307  g_Compen                       10 200   8  Zero  ZI  main.o
  00086308-00086e17  g_Navi                          2 832   8  Zero  ZI  main.o
  00086e18-00087597  g_CmdFullTempCompenData         1 920   8  Zero  ZI  main.o
  00087598-00087c9f  g_GyroANNCompen                 1 800   8  Zero  ZI  main.o
  00087ca0-000883a7  g_AccANNCompen                  1 800   8  Zero  ZI  main.o
  000883a8-00088977  g_ZUPT                          1 488   8  Zero  ZI  main.o
  00088978-00088cb7  g_InertialSysAlign                832   8  Zero  ZI  main.o
  00088cb8-00088e77  g_SysVar                          448   8  Zero  ZI  main.o
  00088e78-00088fd7  g_CmdNormalTempCompenData         352   8  Zero  ZI  main.o
  00088fd8-0008908f  g_Align                           184   8  Zero  ZI  main.o
  00089090-0008910f  g_SelfTest                        128   8  Zero  ZI  main.o
  00089110-0008915f  g_InitBind                         80   8  Zero  ZI  main.o
  00089160-00089177  r_LastGyro                         24   8  Zero  ZI  arithmetic.o
  00089178-0008918f  r_Gyro                             24   8  Zero  ZI  arithmetic.o
  00089190-000891a7  gyro_sum                           24   8  Zero  ZI  arithmetic.o
  000891a8-000891bf  estimated_gyro_bias                24   8  Zero  ZI  arithmetic.o
  000891c0-000891d7  estimated_acc_bias                 24   8  Zero  ZI  arithmetic.o
  000891d8-000891ef  acc_sum                            24   8  Zero  ZI  arithmetic.o
  000891f0-00089207  LastAcc                            24   8  Zero  ZI  arithmetic.o
  00089208-0008921f  Acc                                24   8  Zero  ZI  arithmetic.o
  00089220-0008a959  stSetPara                       5 946   4  Zero  ZI  SetParaBao.o
  0008a95a-0008a95b  tx_counter                          2   2  Zero  ZI  Uart_Irq.o
  0008a95c-0008ad5b  gframeParsebuf                  1 024   4  Zero  ZI  Uart_Irq.o
  0008ad5c-0008ae5b  s_xpi_nor_config                  256   4  Zero  ZI  flash.o
  0008ae5c-0008aeab  InavOutData                        80   4  Zero  ZI  arithmetic.o
  0008aeac-0008aecb  combineData                        32   4  Zero  ZI  arithmetic.o
  0008aecc-0008aee7  stSmi240Data                       28   4  Zero  ZI  protocol.o
  0008aee8-0008af03  ImuData                            28   4  Zero  ZI  main.o
  0008af04-0008af1b  __SEGGER_RTL_aSigTab               24   4  Zero  ZI  execops.o (libc_rv32imac_balanced.a)
  0008af1c-0008af27  control_config                     12   4  Zero  ZI  spi.o
  0008af28-0008af33  control_Slave_config               12   4  Zero  ZI  spi.o
  0008af34-0008af37  timer_cb                            4   4  Zero  ZI  board.c.o
  0008af38-0008af3b  hpm_core_clock                      4   4  Zero  ZI  hpm_clock_drv.c.o
  0008af3c-0008af3f  grxst                               4   4  Zero  ZI  Uart_Irq.o
  0008af40-0008af43  grxlen                              4   4  Zero  ZI  Uart_Irq.o
  0008af44-0008af47  g_console_uart                      4   4  Zero  ZI  hpm_debug_console.c.o
  0008af48-0008af4b  flag.2                              4   4  Zero  ZI  SetParaBao.o
  0008af4c-0008af4f  bias_sample_count                   4   4  Zero  ZI  arithmetic.o
  0008af50-0008af53  bias_estimate_done                  4   4  Zero  ZI  arithmetic.o
  0008af54-0008af57  __SEGGER_RTL_stdout_file            4   4  Zero  ZI  hpm_debug_console.c.o
  0008af58-0008af5b  __SEGGER_RTL_locale_ptr             4   4  Zero  ZI  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0008af5c-0008af5f  __SEGGER_RTL_heap_globals           4   4  Zero  ZI  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0008af60-0008af63  TimeStamp                           4   4  Zero  ZI  main.o
  0008af64-0008af65  tCnt.0                              2   2  Zero  ZI  main.o
  0008af66-0008af66  uart_rx_dma_done                    1   1  Zero  ZI  uart_dma.o
  0008af67-0008af67  g_ucSystemResetFlag                 1   1  Zero  ZI  SetParaBao.o
  0008af68-0008af68  g_UpdateSuccessful                  1   1  Zero  ZI  SetParaBao.o
  0008af69-0008af69  g_StartUpdateFirm                   1   1  Zero  ZI  SetParaBao.o
  0008af6a-0008af6a  fpga_syn                            1   1  Zero  ZI  main.o
  0008af6b-0008af6b  uart_tx_dma_done                    1   1  Init  RW  uart_dma.o
  0008af6c-0008af6f  uiLastBaoInDex.3                    4   4  Init  RW  SetParaBao.o
  0008af70-0008af73  stdout                              4   4  Init  RW  hpm_debug_console.c.o
  0008af74-0008af77  nbr_data_to_send                    4   4  Init  RW  Uart_Irq.o
  0008af78-0008af7b  gbtxcompleted                       4   4  Init  RW  Uart_Irq.o
  0008af7c-0008af7c  g_UpdateBackFlag                    1   1  Init  RW  SetParaBao.o
  0008af7d-0008af7f  ( UNUSED .=.+3 )                    3   -  ----  -   -
  0008af80-0008cf7f  grxbuffer                       8 192   4  None  ZI  Uart_Irq.o
  0008cf80-00090f7f  [.bss.block.heap]              16 384   8  None  ZI  [ Linker created ]
  00090f80-0009bfff  ( UNUSED .=.+45184 )           45 184   -  ----  -   -
  0009c000-0009ffff  [.bss.block.stack]             16 384  16  None  ZI  [ Linker created ]
  8000d000-8000d00f  option                             16   4  Cnst  RO  board.c.o
  8000d010-8000dfff  ( UNUSED .=.+4080 )             4 080   -  ----  -   -
  8000e000-8000e00f  header                             16   4  Cnst  RO  hpm_bootheader.c.o
  8000e010-8000e08f  fw_info                           128   4  Cnst  RO  hpm_bootheader.c.o
  8000e090-8000ffff  ( UNUSED .=.+8048 )             8 048   -  ----  -   -
  80010000-80010065  _start                            102   2  Code  RX  startup.s.o
  80010066-80010067  __SEGGER_RTL_SIGNAL_SIG_DFL
                                                         2   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80010068-8001008f  [.rodata]                          40   8  Cnst  RO  align.o
  80010090-800100af  [.rodata]                          32   8  Cnst  RO  arithmetic.o
  800100b0-80010147  [.rodata]                         152   8  Cnst  RO  navi.o
  80010148-8001046f  [.rodata]                         808   8  Cnst  RO  ZUPT.o
  80010470-80010487  [.rodata]                          24   8  Cnst  RO  compen.o
  80010488-8001048f  [.rodata]                           8   8  Cnst  RO  matvecmath.o
  80010490-800104af  [.rodata]                          32   8  Cnst  RO  protocol.o
  800104b0-800104c7  [.rodata]                          24   8  Cnst  RO  hpm_pllctlv2_drv.c.o
  800104c8-80010527  __SEGGER_RTL_float64_ATan          96   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010528-8001055f  __SEGGER_RTL_float64_Tan           56   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010560-800105cf  __SEGGER_RTL_float64_ASinACos
                                                       112   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  800105d0-8001060f  __SEGGER_RTL_float64_SinCos
                                                        64   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010610-800106af  __SEGGER_RTL_ipow10               160   8  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  800106b0-8001070f  [.srodata.merged.cst8]             96   8  Cnst  RO  [ Linker created ]
  80010710-8001082f  [.rodata]                         288   4  Cnst  RO  flash.o
  80010830-80010bf7  [.rodata]                         968   4  Cnst  RO  main.o
  80010bf8-80010d43  [.rodata.SetParaTemperCompen]
                                                       332   4  Cnst  RO  SetParaBao.o
  80010d44-80010d6f  [.rodata]                          44   4  Cnst  RO  Smi980.o
  80010d70-8001111b  [.rodata]                         940   4  Cnst  RO  board.c.o
  8001111c-8001112f  [.rodata.uart_init]                20   4  Cnst  RO  hpm_uart_drv.c.o
  80011130-80011133  [.rodata]                           4   4  Cnst  RO  hpm_uart_drv.c.o
  80011134-80011173  [.rodata.exception_handler]
                                                        64   4  Cnst  RO  trap.c.o
  80011174-8001117b  s_wdgs                              8   4  Cnst  RO  hpm_clock_drv.c.o
  8001117c-800111ab  [.rodata.clock_get_frequency]
                                                        48   4  Cnst  RO  hpm_clock_drv.c.o
  800111ac-800111cb  [.rodata.get_frequency_for_source]
                                                        32   4  Cnst  RO  hpm_clock_drv.c.o
  800111cc-800111fb  [.rodata.clock_set_source_divider]
                                                        48   4  Cnst  RO  hpm_clock_drv.c.o
  800111fc-800112fb  __SEGGER_RTL_fdiv_reciprocal_table
                                                       256   4  Cnst  RO  floatasmops_rv.o (libc_rv32imac_balanced.a)
  800112fc-8001147b  __SEGGER_RTL_aSqrtData            384   4  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  8001147c-8001187b  __SEGGER_RTL_Moeller_inverse_lut
                                                     1 024   4  Cnst  RO  intops.o (libc_rv32imac_balanced.a)
  8001187c-80011893  __SEGGER_RTL_aPower2f              24   4  Cnst  RO  utilops.o (libc_rv32imac_balanced.a)
  80011894-800118a3  __SEGGER_RTL_hex_lc                16   4  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  800118a4-800118b3  __SEGGER_RTL_hex_uc                16   4  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  800118b4-800118e3  [.rodata.libc.__SEGGER_RTL_vfprintf_short_float_long.str1.4]
                                                        48   4  Cnst  RO  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  800118e4-800119ab  [.rodata.libc.__SEGGER_RTL_vfprintf_short_float_long]
                                                       200   4  Cnst  RO  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  800119ac-800119b7  __SEGGER_RTL_c_locale              12   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800119b8-80011a0f  __SEGGER_RTL_c_locale_data
                                                        88   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011a10-80011a2f  __SEGGER_RTL_codeset_ascii
                                                        32   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011a30-80011aaf  __SEGGER_RTL_ascii_ctype_map
                                                       128   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011ab0-80011acb  [.srodata.merged.cst4]             28   4  Cnst  RO  [ Linker created ]
  80011acc-80011ae5  [.rodata]                          26   4  Cnst  RO  Uart_Irq.o
  80011ae6-80011c27  ComputeCen                        322   2  Code  RX  align.o
  80011c28-80011c29  s_adc_clk_mux_node                  2   4  Cnst  RO  hpm_clock_drv.c.o
  80011c2a-80011cfd  InertialSysAlign_Init             212   2  Code  RX  align.o
  80011cfe-80011cff  __SEGGER_RTL_SIGNAL_SIG_IGN
                                                         2   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80011d00-80011d01  s_dac_clk_mux_node                  2   4  Cnst  RO  hpm_clock_drv.c.o
  80011d02-80011f2f  InertialSysAlignCompute           558   2  Code  RX  align.o
  80011f30-80011f4d  [.rodata.libc.__SEGGER_RTL_X_assert.str1.4]
                                                        30   4  Cnst  RO  execops.o (libc_rv32imac_balanced.a)
  80011f4e-8001203f  ComputeVi                         242   2  Code  RX  align.o
  80012040-80012049  [.rodata.libc.__SEGGER_RTL_find_locale.str1.4]
                                                        10   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8001204a-800120e3  ComputeVib0                       154   2  Code  RX  align.o
  800120e4-800120e5  __SEGGER_RTL_data_utf8_period
                                                         2   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800120e6-8001295f  NavDataOutputSet                2 170   2  Code  RX  arithmetic.o
  80012960-80012999  __SEGGER_RTL_c_locale_day_names
                                                        58   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8001299a-80012b81  AlgorithmAct                      488   2  Code  RX  arithmetic.o
  80012b82-80012b83  __SEGGER_RTL_SIGNAL_SIG_ERR
                                                         2   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80012b84-80012b84  __SEGGER_RTL_data_empty_string
                                                         1   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012b85-80012b85  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012b86-80012bc9  INS600mAlgorithmEntry              68   2  Code  RX  arithmetic.o
  80012bca-80012bcb  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80012bcc-80012bfc  __SEGGER_RTL_c_locale_abbrev_month_names
                                                        49   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012bfd-80012bfd  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012bfe-80012c87  SysVarDefaultSet                  138   2  Code  RX  navi.o
  80012c88-80012ca4  __SEGGER_RTL_c_locale_abbrev_day_names
                                                        29   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012ca5-80012ca5  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012ca6-80012d51  Sys_Init                          172   2  Code  RX  navi.o
  80012d52-80012d53  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80012d54-80012d60  __SEGGER_RTL_ascii_ctype_mask
                                                        13   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012d61-80012d61  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012d62-80012e8b  Navi_Init                         298   2  Code  RX  navi.o
  80012e8c-80012e94  __SEGGER_RTL_c_locale_time_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012e95-80012e95  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012e96-80012f67  ComputeDelSenbb                   210   2  Code  RX  navi.o
  80012f68-80012f70  __SEGGER_RTL_c_locale_date_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012f71-80012f71  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012f72-80013287  ComputeQ                          790   2  Code  RX  navi.o
  80013288-800136a2  [.rodata]                       1 051   4  Cnst  RO  spi.o
  800136a3-800136a3  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800136a4-8001375e  [.rodata]                         187   4  Cnst  RO  hpm_l1c_drv.c.o
  8001375f-8001375f  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80013760-8001376e  __SEGGER_RTL_c_locale_date_time_format
                                                        15   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8001376f-8001376f  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80013770-80013776  __SEGGER_RTL_c_locale_am_pm_indicator
                                                         7   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80013777-80013777  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80013778-800137ce  __SEGGER_RTL_c_locale_month_names
                                                        87   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800137cf-800137cf  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800137d0-800138b1  ComputeWnbb                       226   2  Code  RX  navi.o
  800138b2-80013d3b  QToCnb                          1 162   2  Code  RX  navi.o
  80013d3c-80013fc1  AttiToCnb                         646   2  Code  RX  navi.o
  80013fc2-8001417f  ZUPTInit                          446   2  Code  RX  ZUPT.o
  80014180-80014849  ZUPTDetection                   1 738   2  Code  RX  ZUPT.o
  8001484a-80014883  rom_xpi_nor_erase_sector           58   2  Code  RX  flash.o
  80014884-800148c5  rom_xpi_nor_program                66   2  Code  RX  flash.o
  800148c6-80014907  norflash_init                      66   2  Code  RX  flash.o
  80014908-8001493d  norflash_read                      54   2  Code  RX  flash.o
  8001493e-8001498b  norflash_read_mem                  78   2  Code  RX  flash.o
  8001498c-800149bf  norflash_write                     52   2  Code  RX  flash.o
  800149c0-800149eb  norflash_erase_sector              44   2  Code  RX  flash.o
  800149ec-80014a49  spi_transfer_mode_print            94   2  Code  RX  spi.o
  80014a4a-80014b49  SpiInitMaster                     256   2  Code  RX  spi.o
  80014b4a-80014c19  SpiInitSlave                      208   2  Code  RX  spi.o
  80014c1a-80014cf3  Timer_Init                        218   2  Code  RX  Timer.o
  80014cf4-80014d05  uart_read_byte                     18   2  Code  RX  Uart_Irq.o
  80014d06-80014d27  uart_check_status                  34   2  Code  RX  Uart_Irq.o
  80014d28-80014d71  UartIrqSendMsg                     74   2  Code  RX  Uart_Irq.o
  80014d72-800150df  analysisRxdata                    878   2  Code  RX  Uart_Irq.o
  800150e0-80016f61  GyroANNCompen_X_Init            7 810   2  Code  RX  AnnTempCompen.o
  80016f62-80018de3  GyroANNCompen_Y_Init            7 810   2  Code  RX  AnnTempCompen.o
  80018de4-8001ac65  GyroANNCompen_Z_Init            7 810   2  Code  RX  AnnTempCompen.o
  8001ac66-8001cae7  AccANNCompen_X_Init             7 810   2  Code  RX  AnnTempCompen.o
  8001cae8-8001e969  AccANNCompen_Y_Init             7 810   2  Code  RX  AnnTempCompen.o
  8001e96a-800207eb  AccANNCompen_Z_Init             7 810   2  Code  RX  AnnTempCompen.o
  800207ec-80020ac5  ANN_Predict                       730   2  Code  RX  AnnTempCompen.o
  80020ac6-80022c5f  Gyro_Compen_Para_Init           8 602   2  Code  RX  compen.o
  80022c60-80024e19  Acc_Compen_Para_Init            8 634   2  Code  RX  compen.o
  80024e1a-80024fc5  GyroCompenCompute                 428   2  Code  RX  compen.o
  80024fc6-8002517f  AccCompenCompute                  442   2  Code  RX  compen.o
  80025180-8002523d  GetTempRangeNum                   190   2  Code  RX  compen.o
  8002523e-8002558b  ComputeAccTempDiff                846   2  Code  RX  compen.o
  8002558c-80025719  RTCompenPara                      398   2  Code  RX  compen.o
  8002571a-800257eb  LinerCompen_60_ANN_Order          210   2  Code  RX  compen.o
  800257ec-800257fd  ppor_sw_reset                      18   2  Code  RX  FirmwareUpdateFile.o
  800257fe-80025811  Drv_SystemReset                    20   2  Code  RX  FirmwareUpdateFile.o
  80025812-800259c5  startup_diagnostics               436   2  Code  RX  main.o
  800259c6-80025ccb  UserTask                          774   2  Code  RX  main.o
  80025ccc-80025dd9  main                              270   2  Code  RX  main.o
  80025dda-80025e53  MultiDim_Vec_Dot                  122   2  Code  RX  matvecmath.o
  80025e54-80025ea9  Mat_Tr                             86   2  Code  RX  matvecmath.o
  80025eaa-8002629b  Mat_Inv                         1 010   2  Code  RX  matvecmath.o
  8002629c-80026581  Qua_Mul                           742   2  Code  RX  matvecmath.o
  80026582-800265bb  Check_8bit                         58   2  Code  RX  protocol.o
  800265bc-800265f5  Check_16bit                        58   2  Code  RX  protocol.o
  800265f6-8002663f  xor_check                          74   2  Code  RX  protocol.o
  80026640-80026713  Smi240UartSend                    212   2  Code  RX  protocol.o
  80026714-800269c1  CombinationSpi2Send               686   2  Code  RX  protocol.o
  800269c2-80026c49  CombinationUartSend22B            648   2  Code  RX  protocol.o
  80026c4a-80026f0f  PureSpi2Send                      710   2  Code  RX  protocol.o
  80026f10-80027391  PureUartSend36B                 1 154   2  Code  RX  protocol.o
  80027392-80027477  SetParaBaud                       230   2  Code  RX  SetParaBao.o
  80027478-80027551  SetParaFrequency                  218   2  Code  RX  SetParaBao.o
  80027552-8002765f  SetParaGnss                       270   2  Code  RX  SetParaBao.o
  80027660-8002776d  SetParaAngle                      270   2  Code  RX  SetParaBao.o
  8002776e-8002787b  SetParaVector                     270   2  Code  RX  SetParaBao.o
  8002787c-80027989  SetParaDeviation                  270   2  Code  RX  SetParaBao.o
  8002798a-80027aff  SetParaGnssInitValue              374   2  Code  RX  SetParaBao.o
  80027b00-80027bd9  SetParaTime                       218   2  Code  RX  SetParaBao.o
  80027bda-80027de3  SaveParaToFlash                   522   2  Code  RX  SetParaBao.o
  80027de4-80027ffd  RestoreFactory                    538   2  Code  RX  SetParaBao.o
  80027ffe-800283b3  SetParaAll                        950   2  Code  RX  SetParaBao.o
  800283b4-800284f1  ReadPara_0                        318   2  Code  RX  SetParaBao.o
  800284f2-800285e7  ReadPara_2                        246   2  Code  RX  SetParaBao.o
  800285e8-800287bd  ReadPara_3                        470   2  Code  RX  SetParaBao.o
  800287be-800288ab  SetParaCalibration                238   2  Code  RX  SetParaBao.o
  800288ac-8002897d  SetParaKalmanQ                    210   2  Code  RX  SetParaBao.o
  8002897e-80028a4f  SetParaKalmanR                    210   2  Code  RX  SetParaBao.o
  80028a50-80028b21  SetParaFilter                     210   2  Code  RX  SetParaBao.o
  80028b22-80028c63  SetParaUpdateStart                322   2  Code  RX  SetParaBao.o
  80028c64-80028d8d  SetParaUpdateSend                 298   2  Code  RX  SetParaBao.o
  80028d8e-80028e87  SetParaUpdateEnd                  250   2  Code  RX  SetParaBao.o
  80028e88-80028f6d  TemperCompenGyroNormal            230   2  Code  RX  SetParaBao.o
  80028f6e-80029053  TemperCompenAccNormal             230   2  Code  RX  SetParaBao.o
  80029054-80029139  TemperCompenGyroAll_0             230   2  Code  RX  SetParaBao.o
  8002913a-8002921f  TemperCompenAccAll_0              230   2  Code  RX  SetParaBao.o
  80029220-80029305  TemperCompenGyroAll_1             230   2  Code  RX  SetParaBao.o
  80029306-800293eb  TemperCompenAccAll_1              230   2  Code  RX  SetParaBao.o
  800293ec-800294d1  TemperCompenGyroAll_2             230   2  Code  RX  SetParaBao.o
  800294d2-800295b7  TemperCompenAccAll_2              230   2  Code  RX  SetParaBao.o
  800295b8-800296a1  TemperCompenGyroAll_3             234   2  Code  RX  SetParaBao.o
  800296a2-8002978b  TemperCompenAccAll_3              234   2  Code  RX  SetParaBao.o
  8002978c-80029875  TemperCompenGyroNerve_X0          234   2  Code  RX  SetParaBao.o
  80029876-8002995f  TemperCompenGyroNerve_Y0          234   2  Code  RX  SetParaBao.o
  80029960-80029a49  TemperCompenGyroNerve_Z0          234   2  Code  RX  SetParaBao.o
  80029a4a-80029b33  TemperCompenAccNerve_X0           234   2  Code  RX  SetParaBao.o
  80029b34-80029c1d  TemperCompenAccNerve_Y0           234   2  Code  RX  SetParaBao.o
  80029c1e-80029d07  TemperCompenAccNerve_Z0           234   2  Code  RX  SetParaBao.o
  80029d08-80029df1  TemperCompenGyroNerve_X1          234   2  Code  RX  SetParaBao.o
  80029df2-80029edb  TemperCompenGyroNerve_Y1          234   2  Code  RX  SetParaBao.o
  80029edc-80029fc5  TemperCompenGyroNerve_Z1          234   2  Code  RX  SetParaBao.o
  80029fc6-8002a0af  TemperCompenAccNerve_Y1           234   2  Code  RX  SetParaBao.o
  8002a0b0-8002a199  TemperCompenAccNerve_Z1           234   2  Code  RX  SetParaBao.o
  8002a19a-8002a283  TemperCompenGyroNerve_X2          234   2  Code  RX  SetParaBao.o
  8002a284-8002a36d  TemperCompenGyroNerve_Y2          234   2  Code  RX  SetParaBao.o
  8002a36e-8002a457  TemperCompenGyroNerve_Z2          234   2  Code  RX  SetParaBao.o
  8002a458-8002a541  TemperCompenAccNerve_X2           234   2  Code  RX  SetParaBao.o
  8002a542-8002a62b  TemperCompenAccNerve_Y2           234   2  Code  RX  SetParaBao.o
  8002a62c-8002a715  TemperCompenAccNerve_Z2           234   2  Code  RX  SetParaBao.o
  8002a716-8002aa87  UartDmaRecSetPara                 882   2  Code  RX  SetParaBao.o
  8002aa88-8002aab1  sysctl_resource_target_is_busy
                                                        42   2  Code  RX  board.c.o
  8002aab2-8002aad7  sysctl_resource_target_get_mode
                                                        38   2  Code  RX  board.c.o
  8002aad8-8002ab01  sysctl_clock_set_preset            42   2  Code  RX  board.c.o
  8002ab02-8002ab27  pllctlv2_xtal_set_rampup_time
                                                        38   2  Code  RX  board.c.o
  8002ab28-8002ab69  board_print_banner                 66   2  Code  RX  board.c.o
  8002ab6a-8002abf3  board_print_clock_freq            138   2  Code  RX  board.c.o
  8002abf4-8002accd  board_init_usb_dp_dm_pins         218   2  Code  RX  board.c.o
  8002acce-8002ace5  board_init_uart                    24   2  Code  RX  board.c.o
  8002ace6-8002ad6f  init_uart_pins                    138   2  Code  RX  pinmux.c.o
  8002ad70-8002ade1  gptmr_channel_get_default_config
                                                       114   2  Code  RX  hpm_gptmr_drv.c.o
  8002ade2-8002af13  gptmr_channel_config              306   2  Code  RX  hpm_gptmr_drv.c.o
  8002af14-8002af85  pllctlv2_set_postdiv              114   2  Code  RX  hpm_pllctlv2_drv.c.o
  8002af86-8002b03f  pllctlv2_get_pll_postdiv_freq_in_hz
                                                       186   2  Code  RX  hpm_pllctlv2_drv.c.o
  8002b040-8002b05d  spi_get_data_length_in_bytes
                                                        30   2  Code  RX  hpm_spi_drv.c.o
  8002b05e-8002b097  spi_get_rx_fifo_valid_data_size
                                                        58   2  Code  RX  hpm_spi_drv.c.o
  8002b098-8002b0d9  spi_write_command                  66   2  Code  RX  hpm_spi_drv.c.o
  8002b0da-8002b11f  spi_read_command                   70   2  Code  RX  hpm_spi_drv.c.o
  8002b120-8002b1e5  spi_write_data                    198   2  Code  RX  hpm_spi_drv.c.o
  8002b1e6-8002b2d1  spi_read_data                     236   2  Code  RX  hpm_spi_drv.c.o
  8002b2d2-8002b423  spi_write_read_data               338   2  Code  RX  hpm_spi_drv.c.o
  8002b424-8002b461  spi_no_data                        62   2  Code  RX  hpm_spi_drv.c.o
  8002b462-8002b47b  spi_master_get_default_timing_config
                                                        26   2  Code  RX  hpm_spi_drv.c.o
  8002b47c-8002b4cd  spi_master_get_default_control_config
                                                        82   2  Code  RX  hpm_spi_drv.c.o
  8002b4ce-8002b507  spi_slave_get_default_control_config
                                                        58   2  Code  RX  hpm_spi_drv.c.o
  8002b508-8002b5a5  spi_master_timing_init            158   2  Code  RX  hpm_spi_drv.c.o
  8002b5a6-8002b767  uart_calculate_baudrate           450   2  Code  RX  hpm_uart_drv.c.o
  8002b768-8002b91f  uart_init                         440   2  Code  RX  hpm_uart_drv.c.o
  8002b920-8002b96d  uart_send_byte                     78   2  Code  RX  hpm_uart_drv.c.o
  8002b96e-8002ba17  _clean_up                         170   2  Code  RX  reset.c.o
  8002ba18-8002ba29  syscall_handler                    18   2  Code  RX  trap.c.o
  8002ba2a-8002baa3  hpm_csr_get_core_cycle            122   2  Code  RX  hpm_clock_drv.c.o
  8002baa4-8002bb55  get_frequency_for_source          178   2  Code  RX  hpm_clock_drv.c.o
  8002bb56-8002bbc1  get_frequency_for_ip_in_common_group
                                                       108   2  Code  RX  hpm_clock_drv.c.o
  8002bbc2-8002bc59  get_frequency_for_adc             152   2  Code  RX  hpm_clock_drv.c.o
  8002bc5a-8002bc8f  get_frequency_for_ewdg             54   2  Code  RX  hpm_clock_drv.c.o
  8002bc90-8002bcd3  get_frequency_for_cpu              68   2  Code  RX  hpm_clock_drv.c.o
  8002bcd4-8002bdd1  clock_set_source_divider          254   2  Code  RX  hpm_clock_drv.c.o
  8002bdd2-8002be0b  clock_add_to_group                 58   2  Code  RX  hpm_clock_drv.c.o
  8002be0c-8002be45  clock_remove_from_group            58   2  Code  RX  hpm_clock_drv.c.o
  8002be46-8002be7b  l1c_dc_enable                      54   2  Code  RX  hpm_l1c_drv.c.o
  8002be7c-8002bea9  l1c_ic_enable                      46   2  Code  RX  hpm_l1c_drv.c.o
  8002beaa-8002beff  l1c_dc_invalidate                  86   2  Code  RX  hpm_l1c_drv.c.o
  8002bf00-8002bf29  sysctl_resource_target_is_busy
                                                        42   2  Code  RX  hpm_sysctl_drv.c.o
  8002bf2a-8002bf4b  sysctl_cpu_clock_any_is_busy
                                                        34   2  Code  RX  hpm_sysctl_drv.c.o
  8002bf4c-8002bf79  sysctl_clock_target_is_busy
                                                        46   2  Code  RX  hpm_sysctl_drv.c.o
  8002bf7a-8002c001  sysctl_config_clock               136   2  Code  RX  hpm_sysctl_drv.c.o
  8002c002-8002c057  system_init                        86   2  Code  RX  system.c.o
  8002c058-8002c0c1  __SEGGER_RTL_xtoa                 106   2  Code  RX  convops.o (libc_rv32imac_balanced.a)
  8002c0c2-8002c0dd  itoa                               28   2  Code  RX  convops.o (libc_rv32imac_balanced.a)
  8002c0de-8002c123  fwrite                             70   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  8002c124-8002c147  fputc                              36   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  8002c148-8002c14f  __subsf3                            8   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c150-8002c159  __subdf3                           10   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c15a-8002c307  __addsf3                          430   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c308-8002c341  __ltsf2                            58   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c342-8002c387  __ltdf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c388-8002c3cd  __ledf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c3ce-8002c3ff  __gtsf2                            50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c400-8002c445  __gtdf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c446-8002c483  __gesf2                            62   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c484-8002c4c9  __gedf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c4ca-8002c513  __fixsfsi                          74   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c514-8002c545  __fixunssfsi                       50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c546-8002c577  __fixunsdfsi                       50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c578-8002c5dd  __floatsisf                       102   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c5de-8002c62b  __floatsidf                        78   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c62c-8002c681  __floatunsisf                      86   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c682-8002c72b  __floatundisf                     170   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c72c-8002c771  __extendsfdf2                      70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c772-8002c7f7  __truncdfsf2                      134   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002c7f8-8002c871  __SEGGER_RTL_ldouble_to_double
                                                       122   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c872-8002c8cf  __SEGGER_RTL_float64_PolyEvalP
                                                        94   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c8d0-8002c9d9  __SEGGER_RTL_float64_sin_inline
                                                       266   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c9da-8002cae7  __SEGGER_RTL_float64_cos_inline
                                                       270   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cae8-8002caf9  __SEGGER_RTL_float32_isnan
                                                        18   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cafa-8002cb07  __SEGGER_RTL_float32_isinf
                                                        14   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cb08-8002cb19  __SEGGER_RTL_float32_isnormal
                                                        18   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cb1a-8002cb6f  ldexp.localalias                   86   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cb70-8002cbd9  floorf                            106   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cbda-8002cd81  atan                              424   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002cd82-8002d067  sqrt                              742   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002d068-8002d06d  asin                                6   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002d06e-8002d093  __ashldi3                          38   2  Code  RX  intasmops_rv.o (libc_rv32imac_balanced.a)
  8002d094-8002d4b3  __udivdi3                       1 056   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002d4b4-8002d8ef  __umoddi3                       1 084   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002d8f0-8002d8f9  abs                                10   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002d8fa-8002d97f  memcpy                            134   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  8002d980-8002d9d9  __SEGGER_RTL_pow10f                90   2  Code  RX  utilops.o (libc_rv32imac_balanced.a)
  8002d9da-8002d9fb  __SEGGER_RTL_prin_flush            34   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002d9fc-8002da15  __SEGGER_RTL_pre_padding           26   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002da16-8002da37  __SEGGER_RTL_init_prin_l           34   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002da38-8002da5d  vfprintf                           38   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002da5e-8002da85  printf                             40   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002da86-8002da99  __SEGGER_init_heap                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  8002da9a-8002daaf  __SEGGER_RTL_init_heap             22   2  Code  RX  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  8002dab0-8002dabd  __SEGGER_RTL_ascii_toupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002dabe-8002dacb  __SEGGER_RTL_ascii_towupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002dacc-8002daf5  __SEGGER_RTL_ascii_mbtowc          42   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002daf6-8002dc29  ComputeCie                        308   2  Code  RX  align.o
  8002dc2a-8002dd61  ComputeCib0i                      312   2  Code  RX  align.o
  8002dd62-8002de2b  FinishInertialSysAlign            202   2  Code  RX  align.o
  8002de2c-8002dfdf  ApplyBiasCorrectionToCombineData
                                                       436   2  Code  RX  arithmetic.o
  8002dfe0-8002e157  IMUdataPredo                      376   2  Code  RX  arithmetic.o
  8002e158-8002e1ab  IMUdataPredp_algParmCache          84   2  Code  RX  arithmetic.o
  8002e1ac-8002e42f  InitialBiasEstimate               644   2  Code  RX  arithmetic.o
  8002e430-8002e43f  AlgorithmDo                        16   2  Code  RX  arithmetic.o
  8002e440-8002e46f  Bind_Init                          48   2  Code  RX  navi.o
  8002e470-8002e60f  NaviCompute                       416   2  Code  RX  navi.o
  8002e610-8002e76f  ComputeG                          352   2  Code  RX  navi.o
  8002e770-8002e867  ComputeRmRn                       248   2  Code  RX  navi.o
  8002e868-8002e8db  ComputeWien                       116   2  Code  RX  navi.o
  8002e8dc-8002e98b  ComputeWenn                       176   2  Code  RX  navi.o
  8002e98c-8002ebf7  CnbToAtti                         620   2  Code  RX  navi.o
  8002ebf8-8002efe7  CnbToQ                          1 008   2  Code  RX  navi.o
  8002efe8-8002eff3  GetZUPTFlag                        12   2  Code  RX  ZUPT.o
  8002eff4-8002f04d  ZUPTAngleConstraint                90   2  Code  RX  ZUPT.o
  8002f04e-8002f089  rom_xpi_nor_read                   60   2  Code  RX  flash.o
  8002f08a-8002f0b1  rom_xpi_nor_auto_config            40   2  Code  RX  flash.o
  8002f0b2-8002f0dd  rom_xpi_nor_get_property           44   2  Code  RX  flash.o
  8002f0de-8002f0ff  norflash_get_chip_size             34   2  Code  RX  flash.o
  8002f100-8002f197  norflash_verify_config_option
                                                       152   2  Code  RX  flash.o
  8002f198-8002f215  norflash_restore_config_option
                                                       126   2  Code  RX  flash.o
  8002f216-8002f255  SpiSlaveSend                       64   2  Code  RX  spi.o
  8002f256-8002f2fd  Smi980SpiTransfer                 168   2  Code  RX  spi.o
  8002f2fe-8002f319  gptmr_enable_irq                   28   2  Code  RX  Timer.o
  8002f31a-8002f33d  gptmr_check_status                 36   2  Code  RX  Timer.o
  8002f33e-8002f351  gptmr_clear_status                 20   2  Code  RX  Timer.o
  8002f352-8002f37d  gptmr_start_counter                44   2  Code  RX  Timer.o
  8002f37e-8002f441  dma_check_transfer_status         196   2  Code  RX  uart_dma.o
  8002f442-8002f459  uart_write_byte                    24   2  Code  RX  Uart_Irq.o
  8002f45a-8002f475  uart_disable_irq                   28   2  Code  RX  Uart_Irq.o
  8002f476-8002f48d  uart_enable_irq                    24   2  Code  RX  Uart_Irq.o
  8002f48e-8002f4a5  uart_get_irq_id                    24   2  Code  RX  Uart_Irq.o
  8002f4a6-8002f5af  UartIrqInit                       266   2  Code  RX  Uart_Irq.o
  8002f5b0-8002f603  ANNCompen_Init                     84   2  Code  RX  AnnTempCompen.o
  8002f604-8002f94b  ComputeGyroTempDiff               840   2  Code  RX  compen.o
  8002f94c-8002f983  Drv_FlashErase                     56   2  Code  RX  FirmwareUpdateFile.o
  8002f984-8002f9c3  Drv_FlashWrite                     64   2  Code  RX  FirmwareUpdateFile.o
  8002f9c4-8002f9df  Drv_FlashRead                      28   2  Code  RX  FirmwareUpdateFile.o
  8002f9e0-8002fab5  TaskMange                         214   2  Code  RX  main.o
  8002fab6-8002fb0b  Spi2Task                           86   2  Code  RX  main.o
  8002fb0c-8002fc23  Vec_Cross                         280   2  Code  RX  matvecmath.o
  8002fc24-8002fcff  Mat_Mul                           220   2  Code  RX  matvecmath.o
  8002fd00-8002fd4b  Relu                               76   2  Code  RX  matvecmath.o
  8002fd4c-8002fdb7  GetSmi240Data                     108   2  Code  RX  protocol.o
  8002fdb8-800300bf  Smi240DataToAlgorithm             776   2  Code  RX  protocol.o
  800300c0-8003018b  Smi240Spi2Send                    204   2  Code  RX  protocol.o
  8003018c-80030447  CombinationUartSend               700   2  Code  RX  protocol.o
  80030448-80030943  PureUartSend                    1 276   2  Code  RX  protocol.o
  80030944-8003097b  crc_verify_8bit                    56   2  Code  RX  SetParaBao.o
  8003097c-80030a07  SendPara_SetHead                  140   2  Code  RX  SetParaBao.o
  80030a08-80030a69  SendPara_SetEnd                    98   2  Code  RX  SetParaBao.o
  80030a6a-80030af5  UpdateStart_SetHead               140   2  Code  RX  SetParaBao.o
  80030af6-80030b57  UpdateStart_SetEnd                 98   2  Code  RX  SetParaBao.o
  80030b58-80030be3  UpdateSend_SetHead                140   2  Code  RX  SetParaBao.o
  80030be4-80030c45  UpdateSend_SetEnd                  98   2  Code  RX  SetParaBao.o
  80030c46-80030cd1  UpdateEnd_SetHead                 140   2  Code  RX  SetParaBao.o
  80030cd2-80030d33  UpdateEnd_SetEnd                   98   2  Code  RX  SetParaBao.o
  80030d34-80030dbf  UpdateStop_SetHead                140   2  Code  RX  SetParaBao.o
  80030dc0-80030e21  UpdateStop_SetEnd                  98   2  Code  RX  SetParaBao.o
  80030e22-80030ead  ReadPara0_SetHead                 140   2  Code  RX  SetParaBao.o
  80030eae-80030f0f  ReadPara0_SetEnd                   98   2  Code  RX  SetParaBao.o
  80030f10-80030f9b  ReadPara1_SetHead                 140   2  Code  RX  SetParaBao.o
  80030f9c-80030ffd  ReadPara1_SetEnd                   98   2  Code  RX  SetParaBao.o
  80030ffe-80031089  ReadPara2_SetHead                 140   2  Code  RX  SetParaBao.o
  8003108a-800310eb  ReadPara2_SetEnd                   98   2  Code  RX  SetParaBao.o
  800310ec-80031177  ReadPara3_SetHead                 140   2  Code  RX  SetParaBao.o
  80031178-800311db  ReadPara3_SetEnd                  100   2  Code  RX  SetParaBao.o
  800311dc-80031267  ReadPara4_SetHead                 140   2  Code  RX  SetParaBao.o
  80031268-800312cb  ReadPara4_SetEnd                  100   2  Code  RX  SetParaBao.o
  800312cc-800313a3  SetParaCoord                      216   2  Code  RX  SetParaBao.o
  800313a4-800314cb  ReadParaFromFlash                 296   2  Code  RX  SetParaBao.o
  800314cc-800315c3  ReadPara_1                        248   2  Code  RX  SetParaBao.o
  800315c4-800316f7  ReadPara_4                        308   2  Code  RX  SetParaBao.o
  800316f8-800317e3  ReadPara                          236   2  Code  RX  SetParaBao.o
  800317e4-800318af  SetParaGpsType                    204   2  Code  RX  SetParaBao.o
  800318b0-8003197b  SetParaDataOutType                204   2  Code  RX  SetParaBao.o
  8003197c-80031a47  SetParaDebugMode                  204   2  Code  RX  SetParaBao.o
  80031a48-80031b13  SetParaGyroType                   204   2  Code  RX  SetParaBao.o
  80031b14-80031c6f  SetParaFactorGyro                 348   2  Code  RX  SetParaBao.o
  80031c70-80031dcb  SetParaFactorAcc                  348   2  Code  RX  SetParaBao.o
  80031dcc-80031f47  ParaUpdateHandle                  380   2  Code  RX  SetParaBao.o
  80031f48-8003200f  SetParaUpdateStop                 200   2  Code  RX  SetParaBao.o
  80032010-800320f5  TemperCompenAccNerve_X1           230   2  Code  RX  SetParaBao.o
  800320f6-80032217  SetParaTemperCompen               290   2  Code  RX  SetParaBao.o
  80032218-80032287  Smi980_Init                       112   2  Code  RX  Smi980.o
  80032288-800323ff  Smi980_ReadData                   376   2  Code  RX  Smi980.o
  80032400-8003241b  sysctl_resource_any_is_busy
                                                        28   2  Code  RX  board.c.o
  8003241c-8003245b  sysctl_resource_target_set_mode
                                                        64   2  Code  RX  board.c.o
  8003245c-8003247f  gptmr_check_status                 36   2  Code  RX  board.c.o
  80032480-80032493  gptmr_clear_status                 20   2  Code  RX  board.c.o
  80032494-800324b3  usb_phy_disable_dp_dm_pulldown
                                                        32   2  Code  RX  board.c.o
  800324b4-800324cf  pllctlv2_xtal_is_stable            28   2  Code  RX  board.c.o
  800324d0-800324eb  pllctlv2_xtal_is_enabled           28   2  Code  RX  board.c.o
  800324ec-80032549  board_init_console                 94   2  Code  RX  board.c.o
  8003254a-80032569  board_init                         32   2  Code  RX  board.c.o
  8003256a-800328c5  board_init_clock                  860   2  Code  RX  board.c.o
  800328c6-800328d9  board_delay_ms                     20   2  Code  RX  board.c.o
  800328da-80032911  board_init_spi_clock               56   2  Code  RX  board.c.o
  80032912-80032923  board_init_spi_pins                18   2  Code  RX  board.c.o
  80032924-80032927  board_init_pmp                      4   2  Code  RX  board.c.o
  80032928-80032a17  board_init_uart_clock             240   2  Code  RX  board.c.o
  80032a18-80032a63  init_py_pins_as_pgpio              76   2  Code  RX  pinmux.c.o
  80032a64-80032ad7  init_spi_pins                     116   2  Code  RX  pinmux.c.o
  80032ad8-80032b3d  console_init                      102   2  Code  RX  hpm_debug_console.c.o
  80032b3e-80032bbb  __SEGGER_RTL_X_file_write         126   2  Code  RX  hpm_debug_console.c.o
  80032bbc-80032bc7  __SEGGER_RTL_X_file_stat           12   2  Code  RX  hpm_debug_console.c.o
  80032bc8-80032bd3  __SEGGER_RTL_X_file_bufsize
                                                        12   2  Code  RX  hpm_debug_console.c.o
  80032bd4-80032c1b  pcfg_dcdc_set_voltage              72   2  Code  RX  hpm_pcfg_drv.c.o
  80032c1c-80032cd3  pllctlv2_init_pll_with_freq
                                                       184   2  Code  RX  hpm_pllctlv2_drv.c.o
  80032cd4-80032dab  pllctlv2_get_pll_freq_in_hz
                                                       216   2  Code  RX  hpm_pllctlv2_drv.c.o
  80032dac-80032dcb  spi_get_data_length_in_bits
                                                        32   2  Code  RX  hpm_spi_drv.c.o
  80032dcc-80032e0b  spi_wait_for_idle_status           64   2  Code  RX  hpm_spi_drv.c.o
  80032e0c-80032e3f  spi_write_address                  52   2  Code  RX  hpm_spi_drv.c.o
  80032e40-80032e83  spi_master_get_default_format_config
                                                        68   2  Code  RX  hpm_spi_drv.c.o
  80032e84-80032ebf  spi_slave_get_default_format_config
                                                        60   2  Code  RX  hpm_spi_drv.c.o
  80032ec0-80032f3f  spi_format_init                   128   2  Code  RX  hpm_spi_drv.c.o
  80032f40-8003305b  spi_control_init                  284   2  Code  RX  hpm_spi_drv.c.o
  8003305c-800331f1  spi_transfer                      406   2  Code  RX  hpm_spi_drv.c.o
  800331f2-8003322d  uart_modem_config                  60   2  Code  RX  hpm_uart_drv.c.o
  8003322e-80033249  uart_disable_irq                   28   2  Code  RX  hpm_uart_drv.c.o
  8003324a-80033261  uart_enable_irq                    24   2  Code  RX  hpm_uart_drv.c.o
  80033262-800332f5  uart_default_config               148   2  Code  RX  hpm_uart_drv.c.o
  800332f6-80033335  uart_flush                         64   2  Code  RX  hpm_uart_drv.c.o
  80033336-80033391  uart_init_rxline_idle_detection
                                                        92   2  Code  RX  hpm_uart_drv.c.o
  80033392-800333a9  reset_handler                      24   2  Code  RX  reset.c.o
  800333aa-800333ad  _init                               4   2  Code  RX  reset.c.o
  800333ae-800333b1  mchtmr_isr                          4   2  Code  RX  trap.c.o
  800333b2-800333b5  swi_isr                             4   2  Code  RX  trap.c.o
  800333b6-800333e1  exception_handler                  44   2  Code  RX  trap.c.o
  800333e2-80033483  clock_get_frequency               162   2  Code  RX  hpm_clock_drv.c.o
  80033484-80033515  get_frequency_for_dac             146   2  Code  RX  hpm_clock_drv.c.o
  80033516-8003353d  get_frequency_for_pewdg            40   2  Code  RX  hpm_clock_drv.c.o
  8003353e-80033569  get_frequency_for_ahb              44   2  Code  RX  hpm_clock_drv.c.o
  8003356a-80033597  clock_check_in_group               46   2  Code  RX  hpm_clock_drv.c.o
  80033598-800335bf  clock_connect_group_to_cpu
                                                        40   2  Code  RX  hpm_clock_drv.c.o
  800335c0-80033697  clock_cpu_delay_ms                216   2  Code  RX  hpm_clock_drv.c.o
  80033698-800336b5  clock_update_core_clock            30   2  Code  RX  hpm_clock_drv.c.o
  800336b6-80033769  l1c_op                            180   2  Code  RX  hpm_l1c_drv.c.o
  8003376a-80033781  l1c_dc_invalidate_all              24   2  Code  RX  hpm_l1c_drv.c.o
  80033782-80033845  sysctl_enable_group_resource
                                                       196   2  Code  RX  hpm_sysctl_drv.c.o
  80033846-800338b9  sysctl_check_group_resource_enable
                                                       116   2  Code  RX  hpm_sysctl_drv.c.o
  800338ba-8003396d  sysctl_config_cpu0_domain_clock
                                                       180   2  Code  RX  hpm_sysctl_drv.c.o
  8003396e-80033999  enable_plic_feature                44   2  Code  RX  system.c.o
  8003399a-800339bd  __SEGGER_RTL_puts_no_nl            36   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  800339be-800339f1  signal                             52   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  800339f2-80033a4d  raise                              92   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80033a4e-80033a57  abort                              10   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80033a58-80033a99  __SEGGER_RTL_X_assert              66   2  Code  RX  execops.o (libc_rv32imac_balanced.a)
  80033a9a-80033aa5  putchar                            12   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  80033aa6-80033adb  puts                               54   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  80033adc-80033db3  __adddf3                          728   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80033db4-80033e63  __mulsf3                          176   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80033e64-80033f73  __muldf3                          272   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80033f74-80034077  __divsf3                          260   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80034078-80034237  __divdf3                          448   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80034238-80034263  __eqsf2                            44   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80034264-800342b3  __fixdfsi                          80   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  800342b4-80034313  __fixunssfdi                       96   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80034314-8003435b  __floatunsidf                      72   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003435c-8003437f  __SEGGER_RTL_SquareHi_U64          36   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034380-800343d9  __SEGGER_RTL_float64_PolyEvalQ
                                                        90   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  800343da-800343fd  __trunctfsf2                       36   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  800343fe-80034401  __SEGGER_RTL_float32_signbit
                                                         4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034402-80034445  ldexpf.localalias                  68   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034446-80034471  frexpf                             44   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034472-80034575  fmodf                             260   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80034576-80034579  sin                                 4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003457a-8003457d  cos                                 4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003457e-800346f9  tan                               380   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  800346fa-800348b1  __SEGGER_RTL_float64_asinacos_fpu
                                                       440   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  800348b2-80034919  memset                            104   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  8003491a-80034981  strlen                            104   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  80034982-80034a19  strnlen                           152   2  Code  RX  strops.o (libc_rv32imac_balanced.a)
  80034a1a-80034a25  __SEGGER_RTL_stream_write          12   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80034a26-80034ac1  __SEGGER_RTL_putc                 156   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80034ac2-80034aeb  __SEGGER_RTL_print_padding
                                                        42   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80034aec-80034b61  vfprintf_l                        118   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80034b62-800357f5  __SEGGER_RTL_vfprintf_short_float_long
                                                     3 220   2  Code  RX  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  800357f6-80035821  __SEGGER_RTL_ascii_isctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035822-80035831  __SEGGER_RTL_ascii_tolower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035832-8003585d  __SEGGER_RTL_ascii_iswctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8003585e-8003586d  __SEGGER_RTL_ascii_towlower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8003586e-80035881  __SEGGER_RTL_ascii_wctomb          20   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035882-80035895  __SEGGER_RTL_current_locale
                                                        20   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80035896-80035897  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80035898-800358eb  __SEGGER_init_table__              84   4  Cnst  RO  [ Linker created ]
  800358ec-80035fb3  __SEGGER_init_data__            1 736   4  Cnst  RO  [ Linker created ]
  80035fb4-80035fc7  __SEGGER_init_zero                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  80035fc8-80035fe3  __SEGGER_init_copy                 28   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)


***********************************************************************************************
***                                                                                         ***
***                                       SYMBOL LIST                                       ***
***                                                                                         ***
***********************************************************************************************

RAM function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  board_timer_isr            0x000004A2          54      2  Init  Gb  board.c.o
  default_isr_19             0x0000041A         136      2  Init  Gb  Uart_Irq.o
  default_isr_34             0x00000232         138      2  Init  Gb  uart_dma.o
  default_isr_5              0x0000015A         136      2  Init  Gb  Timer.o
  default_isr_8              0x000004D8         136      2  Init  Gb  board.c.o
  dma_isr                    0x000001E2          80      2  Init  Gb  uart_dma.o
  irq_handler_trap           0x00000568         312      4  Init  Gb  trap.c.o
  tick_ms_isr                0x00000124          54      2  Init  Gb  Timer.o
  uart_isr                   0x000002BC         350      2  Init  Gb  Uart_Irq.o

RAM function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x00000124  tick_ms_isr                        54      2  Init  Gb  Timer.o
  0x0000015A  default_isr_5                     136      2  Init  Gb  Timer.o
  0x000001E2  dma_isr                            80      2  Init  Gb  uart_dma.o
  0x00000232  default_isr_34                    138      2  Init  Gb  uart_dma.o
  0x000002BC  uart_isr                          350      2  Init  Gb  Uart_Irq.o
  0x0000041A  default_isr_19                    136      2  Init  Gb  Uart_Irq.o
  0x000004A2  board_timer_isr                    54      2  Init  Gb  board.c.o
  0x000004D8  default_isr_8                     136      2  Init  Gb  board.c.o
  0x00000568  irq_handler_trap                  312      4  Init  Gb  trap.c.o

RAM function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  uart_isr                          350      2  Init  Gb  Uart_Irq.o
  irq_handler_trap                  312      4  Init  Gb  trap.c.o
  default_isr_34                    138      2  Init  Gb  uart_dma.o
  default_isr_19                    136      2  Init  Gb  Uart_Irq.o
  default_isr_5                     136      2  Init  Gb  Timer.o
  default_isr_8                     136      2  Init  Gb  board.c.o
  dma_isr                            80      2  Init  Gb  uart_dma.o
  board_timer_isr                    54      2  Init  Gb  board.c.o
  tick_ms_isr                        54      2  Init  Gb  Timer.o

Function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  ANNCompen_Init             0x8002F5B0         108      2  Code  Gb  AnnTempCompen.o
  ANN_Predict                0x800207EC         818      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_X_Init        0x8001AC66       8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Y_Init        0x8001CAE8       8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Z_Init        0x8001E96A       8 134      2  Code  Gb  AnnTempCompen.o
  AccCompenCompute           0x80024FC6         490      2  Code  Gb  compen.o
  Acc_Compen_Para_Init       0x80022C60       9 246      2  Code  Gb  compen.o
  AlgorithmAct               0x8001299A         634      2  Code  Gb  arithmetic.o
  AlgorithmDo                0x8002E430          20      2  Code  Gb  arithmetic.o
  ApplyBiasCorrectionToCombineData
                             0x8002DE2C         472      2  Code  Gb  arithmetic.o
  AttiToCnb                  0x80013D3C         750      2  Code  Gb  navi.o
  Bind_Init                  0x8002E440          56      2  Code  Gb  navi.o
  Check_16bit                0x800265BC          58      2  Code  Gb  protocol.o
  Check_8bit                 0x80026582          58      2  Code  Gb  protocol.o
  CnbToAtti                  0x8002E98C         728      2  Code  Gb  navi.o
  CnbToQ                     0x8002EBF8       1 224      2  Code  Gb  navi.o
  CombinationSpi2Send        0x80026714         806      2  Code  Gb  protocol.o
  CombinationUartSend        0x8003018C         816      2  Code  Gb  protocol.o
  CombinationUartSend22B     0x800269C2         758      2  Code  Gb  protocol.o
  ComputeAccTempDiff         0x8002523E         886      2  Code  Gb  compen.o
  ComputeCen                 0x80011AE6         354      2  Code  Gb  align.o
  ComputeCib0i               0x8002DC2A         336      2  Code  Gb  align.o
  ComputeCie                 0x8002DAF6         340      2  Code  Gb  align.o
  ComputeDelSenbb            0x80012E96         230      2  Code  Gb  navi.o
  ComputeG                   0x8002E610         444      2  Code  Gb  navi.o
  ComputeGyroTempDiff        0x8002F604         880      2  Code  Gb  compen.o
  ComputeQ                   0x80012F72         926      2  Code  Gb  navi.o
  ComputeRmRn                0x8002E770         312      2  Code  Gb  navi.o
  ComputeVi                  0x80011F4E         266      2  Code  Gb  align.o
  ComputeVib0                0x8001204A         166      2  Code  Gb  align.o
  ComputeWenn                0x8002E8DC         192      2  Code  Gb  navi.o
  ComputeWien                0x8002E868         140      2  Code  Gb  navi.o
  ComputeWnbb                0x800137D0         238      2  Code  Gb  navi.o
  Drv_FlashErase             0x8002F94C          60      2  Code  Gb  FirmwareUpdateFile.o
  Drv_FlashRead              0x8002F9C4          32      2  Code  Gb  FirmwareUpdateFile.o
  Drv_FlashWrite             0x8002F984          68      2  Code  Gb  FirmwareUpdateFile.o
  Drv_SystemReset            0x800257FE          26      2  Code  Gb  FirmwareUpdateFile.o
  FinishInertialSysAlign     0x8002DD62         236      2  Code  Gb  align.o
  GetSmi240Data              0x8002FD4C         108      2  Code  Gb  protocol.o
  GetTempRangeNum            0x80025180         194      2  Code  Gb  compen.o
  GetZUPTFlag                0x8002EFE8          16      2  Code  Gb  ZUPT.o
  GyroANNCompen_X_Init       0x800150E0       8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Y_Init       0x80016F62       8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Z_Init       0x80018DE4       8 134      2  Code  Gb  AnnTempCompen.o
  GyroCompenCompute          0x80024E1A         474      2  Code  Gb  compen.o
  Gyro_Compen_Para_Init      0x80020AC6       9 214      2  Code  Gb  compen.o
  IMUdataPredo               0x8002DFE0         468      2  Code  Gb  arithmetic.o
  IMUdataPredp_algParmCache  0x8002E158         100      2  Code  Gb  arithmetic.o
  INS600mAlgorithmEntry      0x80012B86          86      2  Code  Gb  arithmetic.o
  InertialSysAlignCompute    0x80011D02         610      2  Code  Gb  align.o
  InertialSysAlign_Init      0x80011C2A         222      2  Code  Gb  align.o
  InitialBiasEstimate        0x8002E1AC         772      2  Code  Gb  arithmetic.o
  LinerCompen_60_ANN_Order   0x8002571A         222      2  Code  Gb  compen.o
  Mat_Inv                    0x80025EAA       1 050      2  Code  Gb  matvecmath.o
  Mat_Mul                    0x8002FC24         228      2  Code  Gb  matvecmath.o
  Mat_Tr                     0x80025E54          86      2  Code  Gb  matvecmath.o
  MultiDim_Vec_Dot           0x80025DDA         130      2  Code  Gb  matvecmath.o
  NavDataOutputSet           0x800120E6       2 318      2  Code  Gb  arithmetic.o
  NaviCompute                0x8002E470         460      2  Code  Gb  navi.o
  Navi_Init                  0x80012D62         314      2  Code  Gb  navi.o
  ParaUpdateHandle           0x80031DCC         400      2  Code  Gb  SetParaBao.o
  PureSpi2Send               0x80026C4A         730      2  Code  Gb  protocol.o
  PureUartSend               0x80030448       1 296      2  Code  Gb  protocol.o
  PureUartSend36B            0x80026F10       1 238      2  Code  Gb  protocol.o
  QToCnb                     0x800138B2       1 342      2  Code  Gb  navi.o
  Qua_Mul                    0x8002629C         854      2  Code  Gb  matvecmath.o
  RTCompenPara               0x8002558C         422      2  Code  Gb  compen.o
  ReadPara                   0x800316F8         268      2  Code  Gb  SetParaBao.o
  ReadPara0_SetEnd           0x80030EAE         112      2  Code  Gb  SetParaBao.o
  ReadPara0_SetHead          0x80030E22         140      2  Code  Gb  SetParaBao.o
  ReadPara1_SetEnd           0x80030F9C         112      2  Code  Gb  SetParaBao.o
  ReadPara1_SetHead          0x80030F10         140      2  Code  Gb  SetParaBao.o
  ReadPara2_SetEnd           0x8003108A         112      2  Code  Gb  SetParaBao.o
  ReadPara2_SetHead          0x80030FFE         140      2  Code  Gb  SetParaBao.o
  ReadPara3_SetEnd           0x80031178         112      2  Code  Gb  SetParaBao.o
  ReadPara3_SetHead          0x800310EC         140      2  Code  Gb  SetParaBao.o
  ReadPara4_SetEnd           0x80031268         112      2  Code  Gb  SetParaBao.o
  ReadPara4_SetHead          0x800311DC         140      2  Code  Gb  SetParaBao.o
  ReadParaFromFlash          0x800313A4         356      2  Code  Gb  SetParaBao.o
  ReadPara_0                 0x800283B4         366      2  Code  Gb  SetParaBao.o
  ReadPara_1                 0x800314CC         308      2  Code  Gb  SetParaBao.o
  ReadPara_2                 0x800284F2         298      2  Code  Gb  SetParaBao.o
  ReadPara_3                 0x800285E8         534      2  Code  Gb  SetParaBao.o
  ReadPara_4                 0x800315C4         368      2  Code  Gb  SetParaBao.o
  Relu                       0x8002FD00          80      2  Code  Gb  matvecmath.o
  RestoreFactory             0x80027DE4         610      2  Code  Gb  SetParaBao.o
  SaveParaToFlash            0x80027BDA         582      2  Code  Gb  SetParaBao.o
  SendPara_SetEnd            0x80030A08         112      2  Code  Gb  SetParaBao.o
  SendPara_SetHead           0x8003097C         140      2  Code  Gb  SetParaBao.o
  SetParaAll                 0x80027FFE       1 098      2  Code  Gb  SetParaBao.o
  SetParaAngle               0x80027660         314      2  Code  Gb  SetParaBao.o
  SetParaBaud                0x80027392         274      2  Code  Gb  SetParaBao.o
  SetParaCalibration         0x800287BE         278      2  Code  Gb  SetParaBao.o
  SetParaCoord               0x800312CC         256      2  Code  Gb  SetParaBao.o
  SetParaDataOutType         0x800318B0         240      2  Code  Gb  SetParaBao.o
  SetParaDebugMode           0x8003197C         240      2  Code  Gb  SetParaBao.o
  SetParaDeviation           0x8002787C         314      2  Code  Gb  SetParaBao.o
  SetParaFactorAcc           0x80031C70         392      2  Code  Gb  SetParaBao.o
  SetParaFactorGyro          0x80031B14         392      2  Code  Gb  SetParaBao.o
  SetParaFilter              0x80028A50         250      2  Code  Gb  SetParaBao.o
  SetParaFrequency           0x80027478         254      2  Code  Gb  SetParaBao.o
  SetParaGnss                0x80027552         314      2  Code  Gb  SetParaBao.o
  SetParaGnssInitValue       0x8002798A         434      2  Code  Gb  SetParaBao.o
  SetParaGpsType             0x800317E4         240      2  Code  Gb  SetParaBao.o
  SetParaGyroType            0x80031A48         240      2  Code  Gb  SetParaBao.o
  SetParaKalmanQ             0x800288AC         250      2  Code  Gb  SetParaBao.o
  SetParaKalmanR             0x8002897E         250      2  Code  Gb  SetParaBao.o
  SetParaTemperCompen        0x800320F6         412      2  Code  Gb  SetParaBao.o
  SetParaTime                0x80027B00         254      2  Code  Gb  SetParaBao.o
  SetParaUpdateEnd           0x80028D8E         290      2  Code  Gb  SetParaBao.o
  SetParaUpdateSend          0x80028C64         334      2  Code  Gb  SetParaBao.o
  SetParaUpdateStart         0x80028B22         358      2  Code  Gb  SetParaBao.o
  SetParaUpdateStop          0x80031F48         232      2  Code  Gb  SetParaBao.o
  SetParaVector              0x8002776E         314      2  Code  Gb  SetParaBao.o
  Smi240DataToAlgorithm      0x8002FDB8         800      2  Code  Gb  protocol.o
  Smi240Spi2Send             0x800300C0         216      2  Code  Gb  protocol.o
  Smi240UartSend             0x80026640         226      2  Code  Gb  protocol.o
  Smi980SpiTransfer          0x8002F256         180      2  Code  Gb  spi.o
  Smi980_Init                0x80032218         152      2  Code  Gb  Smi980.o
  Smi980_ReadData            0x80032288         436      2  Code  Gb  Smi980.o
  Spi2Task                   0x8002FAB6         100      2  Code  Lc  main.o
  SpiInitMaster              0x80014A4A         314      2  Code  Gb  spi.o
  SpiInitSlave               0x80014B4A         250      2  Code  Gb  spi.o
  SpiSlaveSend               0x8002F216          72      2  Code  Gb  spi.o
  SysVarDefaultSet           0x80012BFE         174      2  Code  Gb  navi.o
  Sys_Init                   0x80012CA6         242      2  Code  Gb  navi.o
  TaskMange                  0x8002F9E0         244      2  Code  Lc  main.o
  TemperCompenAccAll_0       0x8002913A         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_1       0x80029306         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_2       0x800294D2         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_3       0x800296A2         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X0    0x80029A4A         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X1    0x80032010         280      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X2    0x8002A458         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y0    0x80029B34         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y1    0x80029FC6         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y2    0x8002A542         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z0    0x80029C1E         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z1    0x8002A0B0         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z2    0x8002A62C         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNormal      0x80028F6E         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_0      0x80029054         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_1      0x80029220         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_2      0x800293EC         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_3      0x800295B8         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X0   0x8002978C         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X1   0x80029D08         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X2   0x8002A19A         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y0   0x80029876         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y1   0x80029DF2         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y2   0x8002A284         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z0   0x80029960         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z1   0x80029EDC         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z2   0x8002A36E         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNormal     0x80028E88         278      2  Code  Gb  SetParaBao.o
  Timer_Init                 0x80014C1A         238      2  Code  Gb  Timer.o
  UartDmaRecSetPara          0x8002A716         998      2  Code  Gb  SetParaBao.o
  UartIrqInit                0x8002F4A6         296      2  Code  Gb  Uart_Irq.o
  UartIrqSendMsg             0x80014D28          82      2  Code  Gb  Uart_Irq.o
  UpdateEnd_SetEnd           0x80030CD2         112      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetHead          0x80030C46         140      2  Code  Gb  SetParaBao.o
  UpdateSend_SetEnd          0x80030BE4         112      2  Code  Gb  SetParaBao.o
  UpdateSend_SetHead         0x80030B58         140      2  Code  Gb  SetParaBao.o
  UpdateStart_SetEnd         0x80030AF6         112      2  Code  Gb  SetParaBao.o
  UpdateStart_SetHead        0x80030A6A         140      2  Code  Gb  SetParaBao.o
  UpdateStop_SetEnd          0x80030DC0         112      2  Code  Gb  SetParaBao.o
  UpdateStop_SetHead         0x80030D34         140      2  Code  Gb  SetParaBao.o
  UserTask                   0x800259C6         862      2  Code  Lc  main.o
  Vec_Cross                  0x8002FB0C         316      2  Code  Gb  matvecmath.o
  ZUPTAngleConstraint        0x8002EFF4         104      2  Code  Gb  ZUPT.o
  ZUPTDetection              0x80014180       2 014      2  Code  Gb  ZUPT.o
  ZUPTInit                   0x80013FC2         446      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_SIGNAL_SIG_DFL
                             0x80010066           2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_ERR
                             0x80012B82           2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_IGN
                             0x80011CFE           2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SquareHi_U64  0x8003435C          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_X_assert      0x80033A58         112      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                             0x80032BC8          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat   0x80032BBC          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_write  0x80032B3E         140      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_ascii_isctype
                             0x800357F6          44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                             0x80035832          44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_mbtowc  0x8002DACC          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_tolower
                             0x80035822          16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_toupper
                             0x8002DAB0          14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towlower
                             0x8003585E          16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towupper
                             0x8002DABE          14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_wctomb  0x8003586E          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_current_locale
                             0x80035882          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isinf
                             0x8002CAFA          14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnan
                             0x8002CAE8          18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnormal
                             0x8002CB08          18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_signbit
                             0x800343FE           4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_PolyEvalP
                             0x8002C872         110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_PolyEvalQ
                             0x80034380         104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_asinacos_fpu
                             0x800346FA         528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_cos_inline
                             0x8002C9DA         354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_sin_inline
                             0x8002C8D0         338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_init_heap     0x8002DA9A          22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_init_prin_l   0x8002DA16          38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ldouble_to_double
                             0x8002C7F8         122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_pow10f        0x8002D980          98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_pre_padding   0x8002D9FC          30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_prin_flush    0x8002D9DA          34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_print_padding
                             0x80034AC2          48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_putc          0x80034A26         160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_puts_no_nl    0x8003399A          44      2  Code  Lc  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_stream_write  0x80034A1A          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf      0x80034B62       3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf_short_float_long
                             0x80034B62       3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_xltoa         0x8002C058         106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_xtoa          0x8002C058         106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  __SEGGER_init_copy         0x80035FC8          28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __SEGGER_init_done         0x80010040                  2  Code  Gb  startup.s.o
  __SEGGER_init_heap         0x8002DA86          26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __SEGGER_init_zero         0x80035FB4          20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __adddf3                   0x80033ADC         728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __addsf3                   0x8002C15A         430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ashldi3                  0x8002D06E          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  __divdf3                   0x80034078         448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __divsf3                   0x80033F74         260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __eqsf2                    0x80034238          44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __extendsfdf2              0x8002C72C          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixdfsi                  0x80034264          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixsfsi                  0x8002C4CA          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunsdfsi               0x8002C546          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfdi               0x800342B4          96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfsi               0x8002C514          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsidf                0x8002C5DE          78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsisf                0x8002C578         102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatundisf              0x8002C682         170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatunsidf              0x80034314          72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatunsisf              0x8002C62C          86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gedf2                    0x8002C484          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gesf2                    0x8002C446          62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtdf2                    0x8002C400          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtsf2                    0x8002C3CE          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ledf2                    0x8002C388          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltdf2                    0x8002C342          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltsf2                    0x8002C308          58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __muldf3                   0x80033E64         272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __mulsf3                   0x80033DB4         176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __nesf2                    0x80034238          44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subdf3                   0x8002C150          14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subsf3                   0x8002C148          14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __truncdfsf2               0x8002C772         134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __trunctfsf2               0x800343DA          44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __udivdi3                  0x8002D094       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  __umoddi3                  0x8002D4B4       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  _clean_up                  0x8002B96E         170      2  Code  Wk  reset.c.o
  _init                      0x800333AA           4      2  Code  Wk  reset.c.o
  _start                     0x80010000         142      2  Code  Gb  startup.s.o
  abort                      0x80033A4E          16      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  abs                        0x8002D8F0          10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  analysisRxdata             0x80014D72         882      2  Code  Gb  Uart_Irq.o
  asin                       0x8002D068          10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  atan                       0x8002CBDA         510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  board_delay_ms             0x800328C6          24      2  Code  Gb  board.c.o
  board_init                 0x8003254A          68      2  Code  Gb  board.c.o
  board_init_clock           0x8003256A       1 112      2  Code  Gb  board.c.o
  board_init_console         0x800324EC         116      2  Code  Gb  board.c.o
  board_init_pmp             0x80032924           4      2  Code  Gb  board.c.o
  board_init_spi_clock       0x800328DA          64      2  Code  Gb  board.c.o
  board_init_spi_pins        0x80032912          24      2  Code  Gb  board.c.o
  board_init_uart            0x8002ACCE          34      2  Code  Gb  board.c.o
  board_init_uart_clock      0x80032928         288      2  Code  Gb  board.c.o
  board_init_usb_dp_dm_pins  0x8002ABF4         282      2  Code  Gb  board.c.o
  board_print_banner         0x8002AB28          94      2  Code  Gb  board.c.o
  board_print_clock_freq     0x8002AB6A         222      2  Code  Gb  board.c.o
  clock_add_to_group         0x8002BDD2          62      2  Code  Gb  hpm_clock_drv.c.o
  clock_check_in_group       0x8003356A          52      2  Code  Gb  hpm_clock_drv.c.o
  clock_connect_group_to_cpu
                             0x80033598          40      2  Code  Gb  hpm_clock_drv.c.o
  clock_cpu_delay_ms         0x800335C0         224      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_frequency        0x800333E2         200      2  Code  Gb  hpm_clock_drv.c.o
  clock_remove_from_group    0x8002BE0C          62      2  Code  Gb  hpm_clock_drv.c.o
  clock_set_source_divider   0x8002BCD4         270      2  Code  Gb  hpm_clock_drv.c.o
  clock_update_core_clock    0x80033698          36      2  Code  Gb  hpm_clock_drv.c.o
  console_init               0x80032AD8         112      2  Code  Gb  hpm_debug_console.c.o
  cos                        0x8003457A           8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  crc_verify_8bit            0x80030944          56      2  Code  Lc  SetParaBao.o
  dma_check_transfer_status  0x8002F37E         196      2  Code  Lc  uart_dma.o
  enable_plic_feature        0x8003396E          44      2  Code  Gb  system.c.o
  exception_handler          0x800333B6          44      2  Code  Wk  trap.c.o
  exit                       0x8001005A           2      2  Code  Gb  startup.s.o
  floorf                     0x8002CB70         106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fmodf                      0x80034472         260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fputc                      0x8002C124          42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  frexpf                     0x80034446          44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fwrite                     0x8002C0DE          78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  get_frequency_for_adc      0x8002BBC2         162      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ahb      0x8003353E          48      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_cpu      0x8002BC90          74      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_dac      0x80033484         156      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ewdg     0x8002BC5A          58      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ip_in_common_group
                             0x8002BB56         114      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_pewdg    0x80033516          40      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_source   0x8002BAA4         206      2  Code  Gb  hpm_clock_drv.c.o
  gptmr_channel_config       0x8002ADE2         306      2  Code  Gb  hpm_gptmr_drv.c.o
  gptmr_channel_get_default_config
                             0x8002AD70         114      2  Code  Gb  hpm_gptmr_drv.c.o
  gptmr_check_status         0x8002F31A          36      2  Code  Lc  Timer.o
  gptmr_check_status         0x8003245C          36      2  Code  Lc  board.c.o
  gptmr_clear_status         0x8002F33E          20      2  Code  Lc  Timer.o
  gptmr_clear_status         0x80032480          20      2  Code  Lc  board.c.o
  gptmr_enable_irq           0x8002F2FE          28      2  Code  Lc  Timer.o
  gptmr_start_counter        0x8002F352          44      2  Code  Lc  Timer.o
  hpm_csr_get_core_cycle     0x8002BA2A         122      2  Code  Lc  hpm_clock_drv.c.o
  init_py_pins_as_pgpio      0x80032A18          76      2  Code  Gb  pinmux.c.o
  init_spi_pins              0x80032A64         116      2  Code  Gb  pinmux.c.o
  init_uart_pins             0x8002ACE6         138      2  Code  Gb  pinmux.c.o
  itoa                       0x8002C0C2          34      2  Code  Wk  convops.o (libc_rv32imac_balanced.a)
  l1c_dc_enable              0x8002BE46          54      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_dc_invalidate          0x8002BEAA          98      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_dc_invalidate_all      0x8003376A          24      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_ic_enable              0x8002BE7C          46      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_op                     0x800336B6         180      2  Code  Lc  hpm_l1c_drv.c.o
  ldexp                      0x8002CB1A          86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexp.localalias           0x8002CB1A          86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ldexpf                     0x80034402          68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexpf.localalias          0x80034402          68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  main                       0x80025CCC         410      2  Code  Gb  main.o
  mchtmr_isr                 0x800333AE           4      2  Code  Wk  trap.c.o
  memcpy                     0x8002D8FA         134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  memset                     0x800348B2         104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  norflash_erase_sector      0x800149C0          50      2  Code  Gb  flash.o
  norflash_get_chip_size     0x8002F0DE          40      2  Code  Gb  flash.o
  norflash_init              0x800148C6          78      2  Code  Gb  flash.o
  norflash_read              0x80014908          58      2  Code  Gb  flash.o
  norflash_read_mem          0x8001493E          86      2  Code  Gb  flash.o
  norflash_restore_config_option
                             0x8002F198         184      2  Code  Gb  flash.o
  norflash_verify_config_option
                             0x8002F100         168      2  Code  Gb  flash.o
  norflash_write             0x8001498C          58      2  Code  Gb  flash.o
  pcfg_dcdc_set_voltage      0x80032BD4          72      2  Code  Gb  hpm_pcfg_drv.c.o
  pllctlv2_get_pll_freq_in_hz
                             0x80032CD4         248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_get_pll_postdiv_freq_in_hz
                             0x8002AF86         222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_init_pll_with_freq
                             0x80032C1C         184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_set_postdiv       0x8002AF14         114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_xtal_is_enabled   0x800324D0          28      2  Code  Lc  board.c.o
  pllctlv2_xtal_is_stable    0x800324B4          28      2  Code  Lc  board.c.o
  pllctlv2_xtal_set_rampup_time
                             0x8002AB02          38      2  Code  Lc  board.c.o
  ppor_sw_reset              0x800257EC          18      2  Code  Lc  FirmwareUpdateFile.o
  printf                     0x8002DA5E          46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  putchar                    0x80033A9A          16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  puts                       0x80033AA6          68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  raise                      0x800339F2         108      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  reset_handler              0x80033392          32      2  Code  Wk  reset.c.o
  rom_xpi_nor_auto_config    0x8002F08A          40      2  Code  Lc  flash.o
  rom_xpi_nor_erase_sector   0x8001484A          58      2  Code  Lc  flash.o
  rom_xpi_nor_get_property   0x8002F0B2          44      2  Code  Lc  flash.o
  rom_xpi_nor_program        0x80014884          66      2  Code  Lc  flash.o
  rom_xpi_nor_read           0x8002F04E          60      2  Code  Lc  flash.o
  signal                     0x800339BE          52      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  sin                        0x80034576           8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  spi_control_init           0x80032F40         284      2  Code  Gb  hpm_spi_drv.c.o
  spi_format_init            0x80032EC0         128      2  Code  Gb  hpm_spi_drv.c.o
  spi_get_data_length_in_bits
                             0x80032DAC          32      2  Code  Lc  hpm_spi_drv.c.o
  spi_get_data_length_in_bytes
                             0x8002B040          34      2  Code  Lc  hpm_spi_drv.c.o
  spi_get_rx_fifo_valid_data_size
                             0x8002B05E          58      2  Code  Lc  hpm_spi_drv.c.o
  spi_master_get_default_control_config
                             0x8002B47C          82      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_get_default_format_config
                             0x80032E40          68      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_get_default_timing_config
                             0x8002B462          26      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_timing_init     0x8002B508         158      2  Code  Gb  hpm_spi_drv.c.o
  spi_no_data                0x8002B424          62      2  Code  Lc  hpm_spi_drv.c.o
  spi_read_command           0x8002B0DA          70      2  Code  Gb  hpm_spi_drv.c.o
  spi_read_data              0x8002B1E6         242      2  Code  Gb  hpm_spi_drv.c.o
  spi_slave_get_default_control_config
                             0x8002B4CE          58      2  Code  Gb  hpm_spi_drv.c.o
  spi_slave_get_default_format_config
                             0x80032E84          60      2  Code  Gb  hpm_spi_drv.c.o
  spi_transfer               0x8003305C         460      2  Code  Gb  hpm_spi_drv.c.o
  spi_transfer_mode_print    0x800149EC         102      2  Code  Gb  spi.o
  spi_wait_for_idle_status   0x80032DCC          64      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_address          0x80032E0C          52      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_command          0x8002B098          66      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_data             0x8002B120         198      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_read_data        0x8002B2D2         338      2  Code  Gb  hpm_spi_drv.c.o
  sqrt                       0x8002CD82         778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  start                      0x80010054                  2  Code  Gb  startup.s.o
  startup_diagnostics        0x80025812         666      2  Code  Lc  main.o
  strlen                     0x8003491A         104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  strnlen                    0x80034982         152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  swi_isr                    0x800333B2           4      2  Code  Wk  trap.c.o
  syscall_handler            0x8002BA18          18      2  Code  Wk  trap.c.o
  sysctl_check_group_resource_enable
                             0x80033846         116      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_clock_set_preset    0x8002AAD8          42      2  Code  Lc  board.c.o
  sysctl_clock_target_is_busy
                             0x8002BF4C          46      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_config_clock        0x8002BF7A         142      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_config_cpu0_domain_clock
                             0x800338BA         188      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_cpu_clock_any_is_busy
                             0x8002BF2A          34      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_enable_group_resource
                             0x80033782         200      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_resource_any_is_busy
                             0x80032400          28      2  Code  Lc  board.c.o
  sysctl_resource_target_get_mode
                             0x8002AAB2          38      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                             0x8002AA88          42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                             0x8002BF00          42      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_resource_target_set_mode
                             0x8003241C          64      2  Code  Lc  board.c.o
  system_init                0x8002C002          90      2  Code  Wk  system.c.o
  tan                        0x8003457E         488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  uart_calculate_baudrate    0x8002B5A6         526      2  Code  Lc  hpm_uart_drv.c.o
  uart_check_status          0x80014D06          34      2  Code  Lc  Uart_Irq.o
  uart_default_config        0x80033262         148      2  Code  Gb  hpm_uart_drv.c.o
  uart_disable_irq           0x8002F45A          28      2  Code  Lc  Uart_Irq.o
  uart_disable_irq           0x8003322E          28      2  Code  Lc  hpm_uart_drv.c.o
  uart_enable_irq            0x8002F476          24      2  Code  Lc  Uart_Irq.o
  uart_enable_irq            0x8003324A          24      2  Code  Lc  hpm_uart_drv.c.o
  uart_flush                 0x800332F6          64      2  Code  Gb  hpm_uart_drv.c.o
  uart_get_irq_id            0x8002F48E          24      2  Code  Lc  Uart_Irq.o
  uart_init                  0x8002B768         454      2  Code  Gb  hpm_uart_drv.c.o
  uart_init_rxline_idle_detection
                             0x80033336         104      2  Code  Gb  hpm_uart_drv.c.o
  uart_modem_config          0x800331F2          60      2  Code  Lc  hpm_uart_drv.c.o
  uart_read_byte             0x80014CF4          18      2  Code  Lc  Uart_Irq.o
  uart_send_byte             0x8002B920          78      2  Code  Gb  hpm_uart_drv.c.o
  uart_write_byte            0x8002F442          24      2  Code  Lc  Uart_Irq.o
  usb_phy_disable_dp_dm_pulldown
                             0x80032494          32      2  Code  Lc  board.c.o
  vfprintf                   0x8002DA38          46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  vfprintf_l                 0x80034AEC         132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  xor_check                  0x800265F6          74      2  Code  Gb  protocol.o

Function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x80010000  _start                            142      2  Code  Gb  startup.s.o
  0x80010040  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  0x80010054  start                                      2  Code  Gb  startup.s.o
  0x8001005A  exit                                2      2  Code  Gb  startup.s.o
  0x80010066  __SEGGER_RTL_SIGNAL_SIG_DFL
                                                  2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80011AE6  ComputeCen                        354      2  Code  Gb  align.o
  0x80011C2A  InertialSysAlign_Init             222      2  Code  Gb  align.o
  0x80011CFE  __SEGGER_RTL_SIGNAL_SIG_IGN
                                                  2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80011D02  InertialSysAlignCompute           610      2  Code  Gb  align.o
  0x80011F4E  ComputeVi                         266      2  Code  Gb  align.o
  0x8001204A  ComputeVib0                       166      2  Code  Gb  align.o
  0x800120E6  NavDataOutputSet                2 318      2  Code  Gb  arithmetic.o
  0x8001299A  AlgorithmAct                      634      2  Code  Gb  arithmetic.o
  0x80012B82  __SEGGER_RTL_SIGNAL_SIG_ERR
                                                  2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80012B86  INS600mAlgorithmEntry              86      2  Code  Gb  arithmetic.o
  0x80012BFE  SysVarDefaultSet                  174      2  Code  Gb  navi.o
  0x80012CA6  Sys_Init                          242      2  Code  Gb  navi.o
  0x80012D62  Navi_Init                         314      2  Code  Gb  navi.o
  0x80012E96  ComputeDelSenbb                   230      2  Code  Gb  navi.o
  0x80012F72  ComputeQ                          926      2  Code  Gb  navi.o
  0x800137D0  ComputeWnbb                       238      2  Code  Gb  navi.o
  0x800138B2  QToCnb                          1 342      2  Code  Gb  navi.o
  0x80013D3C  AttiToCnb                         750      2  Code  Gb  navi.o
  0x80013FC2  ZUPTInit                          446      2  Code  Gb  ZUPT.o
  0x80014180  ZUPTDetection                   2 014      2  Code  Gb  ZUPT.o
  0x8001484A  rom_xpi_nor_erase_sector           58      2  Code  Lc  flash.o
  0x80014884  rom_xpi_nor_program                66      2  Code  Lc  flash.o
  0x800148C6  norflash_init                      78      2  Code  Gb  flash.o
  0x80014908  norflash_read                      58      2  Code  Gb  flash.o
  0x8001493E  norflash_read_mem                  86      2  Code  Gb  flash.o
  0x8001498C  norflash_write                     58      2  Code  Gb  flash.o
  0x800149C0  norflash_erase_sector              50      2  Code  Gb  flash.o
  0x800149EC  spi_transfer_mode_print           102      2  Code  Gb  spi.o
  0x80014A4A  SpiInitMaster                     314      2  Code  Gb  spi.o
  0x80014B4A  SpiInitSlave                      250      2  Code  Gb  spi.o
  0x80014C1A  Timer_Init                        238      2  Code  Gb  Timer.o
  0x80014CF4  uart_read_byte                     18      2  Code  Lc  Uart_Irq.o
  0x80014D06  uart_check_status                  34      2  Code  Lc  Uart_Irq.o
  0x80014D28  UartIrqSendMsg                     82      2  Code  Gb  Uart_Irq.o
  0x80014D72  analysisRxdata                    882      2  Code  Gb  Uart_Irq.o
  0x800150E0  GyroANNCompen_X_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x80016F62  GyroANNCompen_Y_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x80018DE4  GyroANNCompen_Z_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x8001AC66  AccANNCompen_X_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x8001CAE8  AccANNCompen_Y_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x8001E96A  AccANNCompen_Z_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x800207EC  ANN_Predict                       818      2  Code  Gb  AnnTempCompen.o
  0x80020AC6  Gyro_Compen_Para_Init           9 214      2  Code  Gb  compen.o
  0x80022C60  Acc_Compen_Para_Init            9 246      2  Code  Gb  compen.o
  0x80024E1A  GyroCompenCompute                 474      2  Code  Gb  compen.o
  0x80024FC6  AccCompenCompute                  490      2  Code  Gb  compen.o
  0x80025180  GetTempRangeNum                   194      2  Code  Gb  compen.o
  0x8002523E  ComputeAccTempDiff                886      2  Code  Gb  compen.o
  0x8002558C  RTCompenPara                      422      2  Code  Gb  compen.o
  0x8002571A  LinerCompen_60_ANN_Order          222      2  Code  Gb  compen.o
  0x800257EC  ppor_sw_reset                      18      2  Code  Lc  FirmwareUpdateFile.o
  0x800257FE  Drv_SystemReset                    26      2  Code  Gb  FirmwareUpdateFile.o
  0x80025812  startup_diagnostics               666      2  Code  Lc  main.o
  0x800259C6  UserTask                          862      2  Code  Lc  main.o
  0x80025CCC  main                              410      2  Code  Gb  main.o
  0x80025DDA  MultiDim_Vec_Dot                  130      2  Code  Gb  matvecmath.o
  0x80025E54  Mat_Tr                             86      2  Code  Gb  matvecmath.o
  0x80025EAA  Mat_Inv                         1 050      2  Code  Gb  matvecmath.o
  0x8002629C  Qua_Mul                           854      2  Code  Gb  matvecmath.o
  0x80026582  Check_8bit                         58      2  Code  Gb  protocol.o
  0x800265BC  Check_16bit                        58      2  Code  Gb  protocol.o
  0x800265F6  xor_check                          74      2  Code  Gb  protocol.o
  0x80026640  Smi240UartSend                    226      2  Code  Gb  protocol.o
  0x80026714  CombinationSpi2Send               806      2  Code  Gb  protocol.o
  0x800269C2  CombinationUartSend22B            758      2  Code  Gb  protocol.o
  0x80026C4A  PureSpi2Send                      730      2  Code  Gb  protocol.o
  0x80026F10  PureUartSend36B                 1 238      2  Code  Gb  protocol.o
  0x80027392  SetParaBaud                       274      2  Code  Gb  SetParaBao.o
  0x80027478  SetParaFrequency                  254      2  Code  Gb  SetParaBao.o
  0x80027552  SetParaGnss                       314      2  Code  Gb  SetParaBao.o
  0x80027660  SetParaAngle                      314      2  Code  Gb  SetParaBao.o
  0x8002776E  SetParaVector                     314      2  Code  Gb  SetParaBao.o
  0x8002787C  SetParaDeviation                  314      2  Code  Gb  SetParaBao.o
  0x8002798A  SetParaGnssInitValue              434      2  Code  Gb  SetParaBao.o
  0x80027B00  SetParaTime                       254      2  Code  Gb  SetParaBao.o
  0x80027BDA  SaveParaToFlash                   582      2  Code  Gb  SetParaBao.o
  0x80027DE4  RestoreFactory                    610      2  Code  Gb  SetParaBao.o
  0x80027FFE  SetParaAll                      1 098      2  Code  Gb  SetParaBao.o
  0x800283B4  ReadPara_0                        366      2  Code  Gb  SetParaBao.o
  0x800284F2  ReadPara_2                        298      2  Code  Gb  SetParaBao.o
  0x800285E8  ReadPara_3                        534      2  Code  Gb  SetParaBao.o
  0x800287BE  SetParaCalibration                278      2  Code  Gb  SetParaBao.o
  0x800288AC  SetParaKalmanQ                    250      2  Code  Gb  SetParaBao.o
  0x8002897E  SetParaKalmanR                    250      2  Code  Gb  SetParaBao.o
  0x80028A50  SetParaFilter                     250      2  Code  Gb  SetParaBao.o
  0x80028B22  SetParaUpdateStart                358      2  Code  Gb  SetParaBao.o
  0x80028C64  SetParaUpdateSend                 334      2  Code  Gb  SetParaBao.o
  0x80028D8E  SetParaUpdateEnd                  290      2  Code  Gb  SetParaBao.o
  0x80028E88  TemperCompenGyroNormal            278      2  Code  Gb  SetParaBao.o
  0x80028F6E  TemperCompenAccNormal             278      2  Code  Gb  SetParaBao.o
  0x80029054  TemperCompenGyroAll_0             278      2  Code  Gb  SetParaBao.o
  0x8002913A  TemperCompenAccAll_0              278      2  Code  Gb  SetParaBao.o
  0x80029220  TemperCompenGyroAll_1             278      2  Code  Gb  SetParaBao.o
  0x80029306  TemperCompenAccAll_1              278      2  Code  Gb  SetParaBao.o
  0x800293EC  TemperCompenGyroAll_2             278      2  Code  Gb  SetParaBao.o
  0x800294D2  TemperCompenAccAll_2              278      2  Code  Gb  SetParaBao.o
  0x800295B8  TemperCompenGyroAll_3             282      2  Code  Gb  SetParaBao.o
  0x800296A2  TemperCompenAccAll_3              282      2  Code  Gb  SetParaBao.o
  0x8002978C  TemperCompenGyroNerve_X0          282      2  Code  Gb  SetParaBao.o
  0x80029876  TemperCompenGyroNerve_Y0          282      2  Code  Gb  SetParaBao.o
  0x80029960  TemperCompenGyroNerve_Z0          282      2  Code  Gb  SetParaBao.o
  0x80029A4A  TemperCompenAccNerve_X0           282      2  Code  Gb  SetParaBao.o
  0x80029B34  TemperCompenAccNerve_Y0           282      2  Code  Gb  SetParaBao.o
  0x80029C1E  TemperCompenAccNerve_Z0           282      2  Code  Gb  SetParaBao.o
  0x80029D08  TemperCompenGyroNerve_X1          282      2  Code  Gb  SetParaBao.o
  0x80029DF2  TemperCompenGyroNerve_Y1          282      2  Code  Gb  SetParaBao.o
  0x80029EDC  TemperCompenGyroNerve_Z1          282      2  Code  Gb  SetParaBao.o
  0x80029FC6  TemperCompenAccNerve_Y1           282      2  Code  Gb  SetParaBao.o
  0x8002A0B0  TemperCompenAccNerve_Z1           282      2  Code  Gb  SetParaBao.o
  0x8002A19A  TemperCompenGyroNerve_X2          282      2  Code  Gb  SetParaBao.o
  0x8002A284  TemperCompenGyroNerve_Y2          282      2  Code  Gb  SetParaBao.o
  0x8002A36E  TemperCompenGyroNerve_Z2          282      2  Code  Gb  SetParaBao.o
  0x8002A458  TemperCompenAccNerve_X2           282      2  Code  Gb  SetParaBao.o
  0x8002A542  TemperCompenAccNerve_Y2           282      2  Code  Gb  SetParaBao.o
  0x8002A62C  TemperCompenAccNerve_Z2           282      2  Code  Gb  SetParaBao.o
  0x8002A716  UartDmaRecSetPara                 998      2  Code  Gb  SetParaBao.o
  0x8002AA88  sysctl_resource_target_is_busy
                                                 42      2  Code  Lc  board.c.o
  0x8002AAB2  sysctl_resource_target_get_mode
                                                 38      2  Code  Lc  board.c.o
  0x8002AAD8  sysctl_clock_set_preset            42      2  Code  Lc  board.c.o
  0x8002AB02  pllctlv2_xtal_set_rampup_time
                                                 38      2  Code  Lc  board.c.o
  0x8002AB28  board_print_banner                 94      2  Code  Gb  board.c.o
  0x8002AB6A  board_print_clock_freq            222      2  Code  Gb  board.c.o
  0x8002ABF4  board_init_usb_dp_dm_pins         282      2  Code  Gb  board.c.o
  0x8002ACCE  board_init_uart                    34      2  Code  Gb  board.c.o
  0x8002ACE6  init_uart_pins                    138      2  Code  Gb  pinmux.c.o
  0x8002AD70  gptmr_channel_get_default_config
                                                114      2  Code  Gb  hpm_gptmr_drv.c.o
  0x8002ADE2  gptmr_channel_config              306      2  Code  Gb  hpm_gptmr_drv.c.o
  0x8002AF14  pllctlv2_set_postdiv              114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x8002AF86  pllctlv2_get_pll_postdiv_freq_in_hz
                                                222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x8002B040  spi_get_data_length_in_bytes
                                                 34      2  Code  Lc  hpm_spi_drv.c.o
  0x8002B05E  spi_get_rx_fifo_valid_data_size
                                                 58      2  Code  Lc  hpm_spi_drv.c.o
  0x8002B098  spi_write_command                  66      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B0DA  spi_read_command                   70      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B120  spi_write_data                    198      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B1E6  spi_read_data                     242      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B2D2  spi_write_read_data               338      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B424  spi_no_data                        62      2  Code  Lc  hpm_spi_drv.c.o
  0x8002B462  spi_master_get_default_timing_config
                                                 26      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B47C  spi_master_get_default_control_config
                                                 82      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B4CE  spi_slave_get_default_control_config
                                                 58      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B508  spi_master_timing_init            158      2  Code  Gb  hpm_spi_drv.c.o
  0x8002B5A6  uart_calculate_baudrate           526      2  Code  Lc  hpm_uart_drv.c.o
  0x8002B768  uart_init                         454      2  Code  Gb  hpm_uart_drv.c.o
  0x8002B920  uart_send_byte                     78      2  Code  Gb  hpm_uart_drv.c.o
  0x8002B96E  _clean_up                         170      2  Code  Wk  reset.c.o
  0x8002BA18  syscall_handler                    18      2  Code  Wk  trap.c.o
  0x8002BA2A  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  0x8002BAA4  get_frequency_for_source          206      2  Code  Gb  hpm_clock_drv.c.o
  0x8002BB56  get_frequency_for_ip_in_common_group
                                                114      2  Code  Lc  hpm_clock_drv.c.o
  0x8002BBC2  get_frequency_for_adc             162      2  Code  Lc  hpm_clock_drv.c.o
  0x8002BC5A  get_frequency_for_ewdg             58      2  Code  Lc  hpm_clock_drv.c.o
  0x8002BC90  get_frequency_for_cpu              74      2  Code  Lc  hpm_clock_drv.c.o
  0x8002BCD4  clock_set_source_divider          270      2  Code  Gb  hpm_clock_drv.c.o
  0x8002BDD2  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  0x8002BE0C  clock_remove_from_group            62      2  Code  Gb  hpm_clock_drv.c.o
  0x8002BE46  l1c_dc_enable                      54      2  Code  Gb  hpm_l1c_drv.c.o
  0x8002BE7C  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.c.o
  0x8002BEAA  l1c_dc_invalidate                  98      2  Code  Gb  hpm_l1c_drv.c.o
  0x8002BF00  sysctl_resource_target_is_busy
                                                 42      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002BF2A  sysctl_cpu_clock_any_is_busy
                                                 34      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002BF4C  sysctl_clock_target_is_busy
                                                 46      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002BF7A  sysctl_config_clock               142      2  Code  Gb  hpm_sysctl_drv.c.o
  0x8002C002  system_init                        90      2  Code  Wk  system.c.o
  0x8002C058  __SEGGER_RTL_xtoa                 106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  0x8002C058  __SEGGER_RTL_xltoa                106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  0x8002C0C2  itoa                               34      2  Code  Wk  convops.o (libc_rv32imac_balanced.a)
  0x8002C0DE  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x8002C124  fputc                              42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x8002C148  __subsf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C150  __subdf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C15A  __addsf3                          430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C308  __ltsf2                            58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C342  __ltdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C388  __ledf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C3CE  __gtsf2                            50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C400  __gtdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C446  __gesf2                            62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C484  __gedf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C4CA  __fixsfsi                          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C514  __fixunssfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C546  __fixunsdfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C578  __floatsisf                       102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C5DE  __floatsidf                        78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C62C  __floatunsisf                      86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C682  __floatundisf                     170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C72C  __extendsfdf2                      70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C772  __truncdfsf2                      134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C7F8  __SEGGER_RTL_ldouble_to_double
                                                122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002C872  __SEGGER_RTL_float64_PolyEvalP
                                                110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002C8D0  __SEGGER_RTL_float64_sin_inline
                                                338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002C9DA  __SEGGER_RTL_float64_cos_inline
                                                354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002CAE8  __SEGGER_RTL_float32_isnan
                                                 18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CAFA  __SEGGER_RTL_float32_isinf
                                                 14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CB08  __SEGGER_RTL_float32_isnormal
                                                 18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CB1A  ldexp.localalias                   86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002CB1A  ldexp                              86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CB70  floorf                            106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CBDA  atan                              510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002CD82  sqrt                              778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002D068  asin                               10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002D06E  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002D094  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002D4B4  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002D8F0  abs                                10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002D8FA  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002D980  __SEGGER_RTL_pow10f                98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  0x8002D9DA  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002D9FC  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002DA16  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002DA38  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002DA5E  printf                             46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  0x8002DA86  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  0x8002DA9A  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0x8002DAB0  __SEGGER_RTL_ascii_toupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002DABE  __SEGGER_RTL_ascii_towupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002DACC  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002DAF6  ComputeCie                        340      2  Code  Gb  align.o
  0x8002DC2A  ComputeCib0i                      336      2  Code  Gb  align.o
  0x8002DD62  FinishInertialSysAlign            236      2  Code  Gb  align.o
  0x8002DE2C  ApplyBiasCorrectionToCombineData
                                                472      2  Code  Gb  arithmetic.o
  0x8002DFE0  IMUdataPredo                      468      2  Code  Gb  arithmetic.o
  0x8002E158  IMUdataPredp_algParmCache         100      2  Code  Gb  arithmetic.o
  0x8002E1AC  InitialBiasEstimate               772      2  Code  Gb  arithmetic.o
  0x8002E430  AlgorithmDo                        20      2  Code  Gb  arithmetic.o
  0x8002E440  Bind_Init                          56      2  Code  Gb  navi.o
  0x8002E470  NaviCompute                       460      2  Code  Gb  navi.o
  0x8002E610  ComputeG                          444      2  Code  Gb  navi.o
  0x8002E770  ComputeRmRn                       312      2  Code  Gb  navi.o
  0x8002E868  ComputeWien                       140      2  Code  Gb  navi.o
  0x8002E8DC  ComputeWenn                       192      2  Code  Gb  navi.o
  0x8002E98C  CnbToAtti                         728      2  Code  Gb  navi.o
  0x8002EBF8  CnbToQ                          1 224      2  Code  Gb  navi.o
  0x8002EFE8  GetZUPTFlag                        16      2  Code  Gb  ZUPT.o
  0x8002EFF4  ZUPTAngleConstraint               104      2  Code  Gb  ZUPT.o
  0x8002F04E  rom_xpi_nor_read                   60      2  Code  Lc  flash.o
  0x8002F08A  rom_xpi_nor_auto_config            40      2  Code  Lc  flash.o
  0x8002F0B2  rom_xpi_nor_get_property           44      2  Code  Lc  flash.o
  0x8002F0DE  norflash_get_chip_size             40      2  Code  Gb  flash.o
  0x8002F100  norflash_verify_config_option
                                                168      2  Code  Gb  flash.o
  0x8002F198  norflash_restore_config_option
                                                184      2  Code  Gb  flash.o
  0x8002F216  SpiSlaveSend                       72      2  Code  Gb  spi.o
  0x8002F256  Smi980SpiTransfer                 180      2  Code  Gb  spi.o
  0x8002F2FE  gptmr_enable_irq                   28      2  Code  Lc  Timer.o
  0x8002F31A  gptmr_check_status                 36      2  Code  Lc  Timer.o
  0x8002F33E  gptmr_clear_status                 20      2  Code  Lc  Timer.o
  0x8002F352  gptmr_start_counter                44      2  Code  Lc  Timer.o
  0x8002F37E  dma_check_transfer_status         196      2  Code  Lc  uart_dma.o
  0x8002F442  uart_write_byte                    24      2  Code  Lc  Uart_Irq.o
  0x8002F45A  uart_disable_irq                   28      2  Code  Lc  Uart_Irq.o
  0x8002F476  uart_enable_irq                    24      2  Code  Lc  Uart_Irq.o
  0x8002F48E  uart_get_irq_id                    24      2  Code  Lc  Uart_Irq.o
  0x8002F4A6  UartIrqInit                       296      2  Code  Gb  Uart_Irq.o
  0x8002F5B0  ANNCompen_Init                    108      2  Code  Gb  AnnTempCompen.o
  0x8002F604  ComputeGyroTempDiff               880      2  Code  Gb  compen.o
  0x8002F94C  Drv_FlashErase                     60      2  Code  Gb  FirmwareUpdateFile.o
  0x8002F984  Drv_FlashWrite                     68      2  Code  Gb  FirmwareUpdateFile.o
  0x8002F9C4  Drv_FlashRead                      32      2  Code  Gb  FirmwareUpdateFile.o
  0x8002F9E0  TaskMange                         244      2  Code  Lc  main.o
  0x8002FAB6  Spi2Task                          100      2  Code  Lc  main.o
  0x8002FB0C  Vec_Cross                         316      2  Code  Gb  matvecmath.o
  0x8002FC24  Mat_Mul                           228      2  Code  Gb  matvecmath.o
  0x8002FD00  Relu                               80      2  Code  Gb  matvecmath.o
  0x8002FD4C  GetSmi240Data                     108      2  Code  Gb  protocol.o
  0x8002FDB8  Smi240DataToAlgorithm             800      2  Code  Gb  protocol.o
  0x800300C0  Smi240Spi2Send                    216      2  Code  Gb  protocol.o
  0x8003018C  CombinationUartSend               816      2  Code  Gb  protocol.o
  0x80030448  PureUartSend                    1 296      2  Code  Gb  protocol.o
  0x80030944  crc_verify_8bit                    56      2  Code  Lc  SetParaBao.o
  0x8003097C  SendPara_SetHead                  140      2  Code  Gb  SetParaBao.o
  0x80030A08  SendPara_SetEnd                   112      2  Code  Gb  SetParaBao.o
  0x80030A6A  UpdateStart_SetHead               140      2  Code  Gb  SetParaBao.o
  0x80030AF6  UpdateStart_SetEnd                112      2  Code  Gb  SetParaBao.o
  0x80030B58  UpdateSend_SetHead                140      2  Code  Gb  SetParaBao.o
  0x80030BE4  UpdateSend_SetEnd                 112      2  Code  Gb  SetParaBao.o
  0x80030C46  UpdateEnd_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80030CD2  UpdateEnd_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x80030D34  UpdateStop_SetHead                140      2  Code  Gb  SetParaBao.o
  0x80030DC0  UpdateStop_SetEnd                 112      2  Code  Gb  SetParaBao.o
  0x80030E22  ReadPara0_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80030EAE  ReadPara0_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x80030F10  ReadPara1_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80030F9C  ReadPara1_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x80030FFE  ReadPara2_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x8003108A  ReadPara2_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800310EC  ReadPara3_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80031178  ReadPara3_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800311DC  ReadPara4_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80031268  ReadPara4_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800312CC  SetParaCoord                      256      2  Code  Gb  SetParaBao.o
  0x800313A4  ReadParaFromFlash                 356      2  Code  Gb  SetParaBao.o
  0x800314CC  ReadPara_1                        308      2  Code  Gb  SetParaBao.o
  0x800315C4  ReadPara_4                        368      2  Code  Gb  SetParaBao.o
  0x800316F8  ReadPara                          268      2  Code  Gb  SetParaBao.o
  0x800317E4  SetParaGpsType                    240      2  Code  Gb  SetParaBao.o
  0x800318B0  SetParaDataOutType                240      2  Code  Gb  SetParaBao.o
  0x8003197C  SetParaDebugMode                  240      2  Code  Gb  SetParaBao.o
  0x80031A48  SetParaGyroType                   240      2  Code  Gb  SetParaBao.o
  0x80031B14  SetParaFactorGyro                 392      2  Code  Gb  SetParaBao.o
  0x80031C70  SetParaFactorAcc                  392      2  Code  Gb  SetParaBao.o
  0x80031DCC  ParaUpdateHandle                  400      2  Code  Gb  SetParaBao.o
  0x80031F48  SetParaUpdateStop                 232      2  Code  Gb  SetParaBao.o
  0x80032010  TemperCompenAccNerve_X1           280      2  Code  Gb  SetParaBao.o
  0x800320F6  SetParaTemperCompen               412      2  Code  Gb  SetParaBao.o
  0x80032218  Smi980_Init                       152      2  Code  Gb  Smi980.o
  0x80032288  Smi980_ReadData                   436      2  Code  Gb  Smi980.o
  0x80032400  sysctl_resource_any_is_busy
                                                 28      2  Code  Lc  board.c.o
  0x8003241C  sysctl_resource_target_set_mode
                                                 64      2  Code  Lc  board.c.o
  0x8003245C  gptmr_check_status                 36      2  Code  Lc  board.c.o
  0x80032480  gptmr_clear_status                 20      2  Code  Lc  board.c.o
  0x80032494  usb_phy_disable_dp_dm_pulldown
                                                 32      2  Code  Lc  board.c.o
  0x800324B4  pllctlv2_xtal_is_stable            28      2  Code  Lc  board.c.o
  0x800324D0  pllctlv2_xtal_is_enabled           28      2  Code  Lc  board.c.o
  0x800324EC  board_init_console                116      2  Code  Gb  board.c.o
  0x8003254A  board_init                         68      2  Code  Gb  board.c.o
  0x8003256A  board_init_clock                1 112      2  Code  Gb  board.c.o
  0x800328C6  board_delay_ms                     24      2  Code  Gb  board.c.o
  0x800328DA  board_init_spi_clock               64      2  Code  Gb  board.c.o
  0x80032912  board_init_spi_pins                24      2  Code  Gb  board.c.o
  0x80032924  board_init_pmp                      4      2  Code  Gb  board.c.o
  0x80032928  board_init_uart_clock             288      2  Code  Gb  board.c.o
  0x80032A18  init_py_pins_as_pgpio              76      2  Code  Gb  pinmux.c.o
  0x80032A64  init_spi_pins                     116      2  Code  Gb  pinmux.c.o
  0x80032AD8  console_init                      112      2  Code  Gb  hpm_debug_console.c.o
  0x80032B3E  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  0x80032BBC  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  0x80032BC8  __SEGGER_RTL_X_file_bufsize
                                                 12      2  Code  Gb  hpm_debug_console.c.o
  0x80032BD4  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  0x80032C1C  pllctlv2_init_pll_with_freq
                                                184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x80032CD4  pllctlv2_get_pll_freq_in_hz
                                                248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x80032DAC  spi_get_data_length_in_bits
                                                 32      2  Code  Lc  hpm_spi_drv.c.o
  0x80032DCC  spi_wait_for_idle_status           64      2  Code  Gb  hpm_spi_drv.c.o
  0x80032E0C  spi_write_address                  52      2  Code  Gb  hpm_spi_drv.c.o
  0x80032E40  spi_master_get_default_format_config
                                                 68      2  Code  Gb  hpm_spi_drv.c.o
  0x80032E84  spi_slave_get_default_format_config
                                                 60      2  Code  Gb  hpm_spi_drv.c.o
  0x80032EC0  spi_format_init                   128      2  Code  Gb  hpm_spi_drv.c.o
  0x80032F40  spi_control_init                  284      2  Code  Gb  hpm_spi_drv.c.o
  0x8003305C  spi_transfer                      460      2  Code  Gb  hpm_spi_drv.c.o
  0x800331F2  uart_modem_config                  60      2  Code  Lc  hpm_uart_drv.c.o
  0x8003322E  uart_disable_irq                   28      2  Code  Lc  hpm_uart_drv.c.o
  0x8003324A  uart_enable_irq                    24      2  Code  Lc  hpm_uart_drv.c.o
  0x80033262  uart_default_config               148      2  Code  Gb  hpm_uart_drv.c.o
  0x800332F6  uart_flush                         64      2  Code  Gb  hpm_uart_drv.c.o
  0x80033336  uart_init_rxline_idle_detection
                                                104      2  Code  Gb  hpm_uart_drv.c.o
  0x80033392  reset_handler                      32      2  Code  Wk  reset.c.o
  0x800333AA  _init                               4      2  Code  Wk  reset.c.o
  0x800333AE  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  0x800333B2  swi_isr                             4      2  Code  Wk  trap.c.o
  0x800333B6  exception_handler                  44      2  Code  Wk  trap.c.o
  0x800333E2  clock_get_frequency               200      2  Code  Gb  hpm_clock_drv.c.o
  0x80033484  get_frequency_for_dac             156      2  Code  Lc  hpm_clock_drv.c.o
  0x80033516  get_frequency_for_pewdg            40      2  Code  Lc  hpm_clock_drv.c.o
  0x8003353E  get_frequency_for_ahb              48      2  Code  Lc  hpm_clock_drv.c.o
  0x8003356A  clock_check_in_group               52      2  Code  Gb  hpm_clock_drv.c.o
  0x80033598  clock_connect_group_to_cpu
                                                 40      2  Code  Gb  hpm_clock_drv.c.o
  0x800335C0  clock_cpu_delay_ms                224      2  Code  Gb  hpm_clock_drv.c.o
  0x80033698  clock_update_core_clock            36      2  Code  Gb  hpm_clock_drv.c.o
  0x800336B6  l1c_op                            180      2  Code  Lc  hpm_l1c_drv.c.o
  0x8003376A  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.c.o
  0x80033782  sysctl_enable_group_resource
                                                200      2  Code  Gb  hpm_sysctl_drv.c.o
  0x80033846  sysctl_check_group_resource_enable
                                                116      2  Code  Gb  hpm_sysctl_drv.c.o
  0x800338BA  sysctl_config_cpu0_domain_clock
                                                188      2  Code  Gb  hpm_sysctl_drv.c.o
  0x8003396E  enable_plic_feature                44      2  Code  Gb  system.c.o
  0x8003399A  __SEGGER_RTL_puts_no_nl            44      2  Code  Lc  execops.o (libc_rv32imac_balanced.a)
  0x800339BE  signal                             52      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x800339F2  raise                             108      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  0x80033A4E  abort                              16      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  0x80033A58  __SEGGER_RTL_X_assert             112      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  0x80033A9A  putchar                            16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x80033AA6  puts                               68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x80033ADC  __adddf3                          728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80033DB4  __mulsf3                          176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80033E64  __muldf3                          272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80033F74  __divsf3                          260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034078  __divdf3                          448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034238  __nesf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034238  __eqsf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034264  __fixdfsi                          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x800342B4  __fixunssfdi                       96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034314  __floatunsidf                      72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003435C  __SEGGER_RTL_SquareHi_U64          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80034380  __SEGGER_RTL_float64_PolyEvalQ
                                                104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x800343DA  __trunctfsf2                       44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x800343FE  __SEGGER_RTL_float32_signbit
                                                  4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80034402  ldexpf.localalias                  68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80034402  ldexpf                             68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80034446  frexpf                             44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80034472  fmodf                             260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80034576  sin                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003457A  cos                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003457E  tan                               488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x800346FA  __SEGGER_RTL_float64_asinacos_fpu
                                                528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x800348B2  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003491A  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x80034982  strnlen                           152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  0x80034A1A  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  0x80034A26  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80034AC2  __SEGGER_RTL_print_padding
                                                 48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80034AEC  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80034B62  __SEGGER_RTL_vfprintf_short_float_long
                                              3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  0x80034B62  __SEGGER_RTL_vfprintf           3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  0x800357F6  __SEGGER_RTL_ascii_isctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035822  __SEGGER_RTL_ascii_tolower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035832  __SEGGER_RTL_ascii_iswctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8003585E  __SEGGER_RTL_ascii_towlower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8003586E  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035882  __SEGGER_RTL_current_locale
                                                 20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035FB4  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  0x80035FC8  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)

Function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  Acc_Compen_Para_Init            9 246      2  Code  Gb  compen.o
  Gyro_Compen_Para_Init           9 214      2  Code  Gb  compen.o
  AccANNCompen_X_Init             8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Y_Init             8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Z_Init             8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_X_Init            8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Y_Init            8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Z_Init            8 134      2  Code  Gb  AnnTempCompen.o
  __SEGGER_RTL_vfprintf           3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf_short_float_long
                                  3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  NavDataOutputSet                2 318      2  Code  Gb  arithmetic.o
  ZUPTDetection                   2 014      2  Code  Gb  ZUPT.o
  QToCnb                          1 342      2  Code  Gb  navi.o
  PureUartSend                    1 296      2  Code  Gb  protocol.o
  PureUartSend36B                 1 238      2  Code  Gb  protocol.o
  CnbToQ                          1 224      2  Code  Gb  navi.o
  board_init_clock                1 112      2  Code  Gb  board.c.o
  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  SetParaAll                      1 098      2  Code  Gb  SetParaBao.o
  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  Mat_Inv                         1 050      2  Code  Gb  matvecmath.o
  UartDmaRecSetPara                 998      2  Code  Gb  SetParaBao.o
  ComputeQ                          926      2  Code  Gb  navi.o
  ComputeAccTempDiff                886      2  Code  Gb  compen.o
  analysisRxdata                    882      2  Code  Gb  Uart_Irq.o
  ComputeGyroTempDiff               880      2  Code  Gb  compen.o
  UserTask                          862      2  Code  Lc  main.o
  Qua_Mul                           854      2  Code  Gb  matvecmath.o
  ANN_Predict                       818      2  Code  Gb  AnnTempCompen.o
  CombinationUartSend               816      2  Code  Gb  protocol.o
  CombinationSpi2Send               806      2  Code  Gb  protocol.o
  Smi240DataToAlgorithm             800      2  Code  Gb  protocol.o
  sqrt                              778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  InitialBiasEstimate               772      2  Code  Gb  arithmetic.o
  CombinationUartSend22B            758      2  Code  Gb  protocol.o
  AttiToCnb                         750      2  Code  Gb  navi.o
  PureSpi2Send                      730      2  Code  Gb  protocol.o
  CnbToAtti                         728      2  Code  Gb  navi.o
  __adddf3                          728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  startup_diagnostics               666      2  Code  Lc  main.o
  AlgorithmAct                      634      2  Code  Gb  arithmetic.o
  InertialSysAlignCompute           610      2  Code  Gb  align.o
  RestoreFactory                    610      2  Code  Gb  SetParaBao.o
  SaveParaToFlash                   582      2  Code  Gb  SetParaBao.o
  ReadPara_3                        534      2  Code  Gb  SetParaBao.o
  __SEGGER_RTL_float64_asinacos_fpu
                                    528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  uart_calculate_baudrate           526      2  Code  Lc  hpm_uart_drv.c.o
  atan                              510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  AccCompenCompute                  490      2  Code  Gb  compen.o
  tan                               488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  GyroCompenCompute                 474      2  Code  Gb  compen.o
  ApplyBiasCorrectionToCombineData
                                    472      2  Code  Gb  arithmetic.o
  IMUdataPredo                      468      2  Code  Gb  arithmetic.o
  NaviCompute                       460      2  Code  Gb  navi.o
  spi_transfer                      460      2  Code  Gb  hpm_spi_drv.c.o
  uart_init                         454      2  Code  Gb  hpm_uart_drv.c.o
  __divdf3                          448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  ZUPTInit                          446      2  Code  Gb  ZUPT.o
  ComputeG                          444      2  Code  Gb  navi.o
  Smi980_ReadData                   436      2  Code  Gb  Smi980.o
  SetParaGnssInitValue              434      2  Code  Gb  SetParaBao.o
  __addsf3                          430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  RTCompenPara                      422      2  Code  Gb  compen.o
  SetParaTemperCompen               412      2  Code  Gb  SetParaBao.o
  main                              410      2  Code  Gb  main.o
  ParaUpdateHandle                  400      2  Code  Gb  SetParaBao.o
  SetParaFactorAcc                  392      2  Code  Gb  SetParaBao.o
  SetParaFactorGyro                 392      2  Code  Gb  SetParaBao.o
  ReadPara_4                        368      2  Code  Gb  SetParaBao.o
  ReadPara_0                        366      2  Code  Gb  SetParaBao.o
  SetParaUpdateStart                358      2  Code  Gb  SetParaBao.o
  ReadParaFromFlash                 356      2  Code  Gb  SetParaBao.o
  ComputeCen                        354      2  Code  Gb  align.o
  __SEGGER_RTL_float64_cos_inline
                                    354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ComputeCie                        340      2  Code  Gb  align.o
  __SEGGER_RTL_float64_sin_inline
                                    338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  spi_write_read_data               338      2  Code  Gb  hpm_spi_drv.c.o
  ComputeCib0i                      336      2  Code  Gb  align.o
  SetParaUpdateSend                 334      2  Code  Gb  SetParaBao.o
  Vec_Cross                         316      2  Code  Gb  matvecmath.o
  Navi_Init                         314      2  Code  Gb  navi.o
  SetParaAngle                      314      2  Code  Gb  SetParaBao.o
  SetParaDeviation                  314      2  Code  Gb  SetParaBao.o
  SetParaGnss                       314      2  Code  Gb  SetParaBao.o
  SetParaVector                     314      2  Code  Gb  SetParaBao.o
  SpiInitMaster                     314      2  Code  Gb  spi.o
  ComputeRmRn                       312      2  Code  Gb  navi.o
  ReadPara_1                        308      2  Code  Gb  SetParaBao.o
  gptmr_channel_config              306      2  Code  Gb  hpm_gptmr_drv.c.o
  ReadPara_2                        298      2  Code  Gb  SetParaBao.o
  UartIrqInit                       296      2  Code  Gb  Uart_Irq.o
  SetParaUpdateEnd                  290      2  Code  Gb  SetParaBao.o
  board_init_uart_clock             288      2  Code  Gb  board.c.o
  spi_control_init                  284      2  Code  Gb  hpm_spi_drv.c.o
  TemperCompenAccAll_3              282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X2           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y1           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y2           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z1           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z2           282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_3             282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X2          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y2          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z2          282      2  Code  Gb  SetParaBao.o
  board_init_usb_dp_dm_pins         282      2  Code  Gb  board.c.o
  TemperCompenAccNerve_X1           280      2  Code  Gb  SetParaBao.o
  SetParaCalibration                278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_0              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_1              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_2              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccNormal             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_0             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_1             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_2             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNormal            278      2  Code  Gb  SetParaBao.o
  SetParaBaud                       274      2  Code  Gb  SetParaBao.o
  __muldf3                          272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  clock_set_source_divider          270      2  Code  Gb  hpm_clock_drv.c.o
  ReadPara                          268      2  Code  Gb  SetParaBao.o
  ComputeVi                         266      2  Code  Gb  align.o
  __divsf3                          260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  fmodf                             260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  SetParaCoord                      256      2  Code  Gb  SetParaBao.o
  SetParaFrequency                  254      2  Code  Gb  SetParaBao.o
  SetParaTime                       254      2  Code  Gb  SetParaBao.o
  SetParaFilter                     250      2  Code  Gb  SetParaBao.o
  SetParaKalmanQ                    250      2  Code  Gb  SetParaBao.o
  SetParaKalmanR                    250      2  Code  Gb  SetParaBao.o
  SpiInitSlave                      250      2  Code  Gb  spi.o
  pllctlv2_get_pll_freq_in_hz
                                    248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  TaskMange                         244      2  Code  Lc  main.o
  Sys_Init                          242      2  Code  Gb  navi.o
  spi_read_data                     242      2  Code  Gb  hpm_spi_drv.c.o
  SetParaDataOutType                240      2  Code  Gb  SetParaBao.o
  SetParaDebugMode                  240      2  Code  Gb  SetParaBao.o
  SetParaGpsType                    240      2  Code  Gb  SetParaBao.o
  SetParaGyroType                   240      2  Code  Gb  SetParaBao.o
  ComputeWnbb                       238      2  Code  Gb  navi.o
  Timer_Init                        238      2  Code  Gb  Timer.o
  FinishInertialSysAlign            236      2  Code  Gb  align.o
  SetParaUpdateStop                 232      2  Code  Gb  SetParaBao.o
  ComputeDelSenbb                   230      2  Code  Gb  navi.o
  Mat_Mul                           228      2  Code  Gb  matvecmath.o
  Smi240UartSend                    226      2  Code  Gb  protocol.o
  clock_cpu_delay_ms                224      2  Code  Gb  hpm_clock_drv.c.o
  InertialSysAlign_Init             222      2  Code  Gb  align.o
  LinerCompen_60_ANN_Order          222      2  Code  Gb  compen.o
  board_print_clock_freq            222      2  Code  Gb  board.c.o
  pllctlv2_get_pll_postdiv_freq_in_hz
                                    222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  Smi240Spi2Send                    216      2  Code  Gb  protocol.o
  get_frequency_for_source          206      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_frequency               200      2  Code  Gb  hpm_clock_drv.c.o
  sysctl_enable_group_resource
                                    200      2  Code  Gb  hpm_sysctl_drv.c.o
  spi_write_data                    198      2  Code  Gb  hpm_spi_drv.c.o
  dma_check_transfer_status         196      2  Code  Lc  uart_dma.o
  GetTempRangeNum                   194      2  Code  Gb  compen.o
  ComputeWenn                       192      2  Code  Gb  navi.o
  sysctl_config_cpu0_domain_clock
                                    188      2  Code  Gb  hpm_sysctl_drv.c.o
  norflash_restore_config_option
                                    184      2  Code  Gb  flash.o
  pllctlv2_init_pll_with_freq
                                    184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  Smi980SpiTransfer                 180      2  Code  Gb  spi.o
  l1c_op                            180      2  Code  Lc  hpm_l1c_drv.c.o
  __mulsf3                          176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  SysVarDefaultSet                  174      2  Code  Gb  navi.o
  __floatundisf                     170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  _clean_up                         170      2  Code  Wk  reset.c.o
  norflash_verify_config_option
                                    168      2  Code  Gb  flash.o
  ComputeVib0                       166      2  Code  Gb  align.o
  get_frequency_for_adc             162      2  Code  Lc  hpm_clock_drv.c.o
  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  spi_master_timing_init            158      2  Code  Gb  hpm_spi_drv.c.o
  get_frequency_for_dac             156      2  Code  Lc  hpm_clock_drv.c.o
  Smi980_Init                       152      2  Code  Gb  Smi980.o
  strnlen                           152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  uart_default_config               148      2  Code  Gb  hpm_uart_drv.c.o
  _start                            142      2  Code  Gb  startup.s.o
  sysctl_config_clock               142      2  Code  Gb  hpm_sysctl_drv.c.o
  ComputeWien                       140      2  Code  Gb  navi.o
  ReadPara0_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara1_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara2_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara3_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara4_SetHead                 140      2  Code  Gb  SetParaBao.o
  SendPara_SetHead                  140      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetHead                 140      2  Code  Gb  SetParaBao.o
  UpdateSend_SetHead                140      2  Code  Gb  SetParaBao.o
  UpdateStart_SetHead               140      2  Code  Gb  SetParaBao.o
  UpdateStop_SetHead                140      2  Code  Gb  SetParaBao.o
  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  init_uart_pins                    138      2  Code  Gb  pinmux.c.o
  __truncdfsf2                      134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  MultiDim_Vec_Dot                  130      2  Code  Gb  matvecmath.o
  spi_format_init                   128      2  Code  Gb  hpm_spi_drv.c.o
  __SEGGER_RTL_ldouble_to_double
                                    122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  board_init_console                116      2  Code  Gb  board.c.o
  init_spi_pins                     116      2  Code  Gb  pinmux.c.o
  sysctl_check_group_resource_enable
                                    116      2  Code  Gb  hpm_sysctl_drv.c.o
  get_frequency_for_ip_in_common_group
                                    114      2  Code  Lc  hpm_clock_drv.c.o
  gptmr_channel_get_default_config
                                    114      2  Code  Gb  hpm_gptmr_drv.c.o
  pllctlv2_set_postdiv              114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  ReadPara0_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara1_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara2_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara3_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara4_SetEnd                  112      2  Code  Gb  SetParaBao.o
  SendPara_SetEnd                   112      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetEnd                  112      2  Code  Gb  SetParaBao.o
  UpdateSend_SetEnd                 112      2  Code  Gb  SetParaBao.o
  UpdateStart_SetEnd                112      2  Code  Gb  SetParaBao.o
  UpdateStop_SetEnd                 112      2  Code  Gb  SetParaBao.o
  __SEGGER_RTL_X_assert             112      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  console_init                      112      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_float64_PolyEvalP
                                    110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ANNCompen_Init                    108      2  Code  Gb  AnnTempCompen.o
  GetSmi240Data                     108      2  Code  Gb  protocol.o
  raise                             108      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_xltoa                106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_xtoa                 106      2  Code  Lc  convops.o (libc_rv32imac_balanced.a)
  floorf                            106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ZUPTAngleConstraint               104      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_float64_PolyEvalQ
                                    104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  uart_init_rxline_idle_detection
                                    104      2  Code  Gb  hpm_uart_drv.c.o
  __floatsisf                       102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  spi_transfer_mode_print           102      2  Code  Gb  spi.o
  IMUdataPredp_algParmCache         100      2  Code  Gb  arithmetic.o
  Spi2Task                          100      2  Code  Lc  main.o
  __SEGGER_RTL_pow10f                98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  l1c_dc_invalidate                  98      2  Code  Gb  hpm_l1c_drv.c.o
  __fixunssfdi                       96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  board_print_banner                 94      2  Code  Gb  board.c.o
  system_init                        90      2  Code  Wk  system.c.o
  INS600mAlgorithmEntry              86      2  Code  Gb  arithmetic.o
  Mat_Tr                             86      2  Code  Gb  matvecmath.o
  __floatunsisf                      86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  ldexp                              86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexp.localalias                   86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  norflash_read_mem                  86      2  Code  Gb  flash.o
  UartIrqSendMsg                     82      2  Code  Gb  Uart_Irq.o
  spi_master_get_default_control_config
                                     82      2  Code  Gb  hpm_spi_drv.c.o
  Relu                               80      2  Code  Gb  matvecmath.o
  __fixdfsi                          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsidf                        78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  norflash_init                      78      2  Code  Gb  flash.o
  uart_send_byte                     78      2  Code  Gb  hpm_uart_drv.c.o
  init_py_pins_as_pgpio              76      2  Code  Gb  pinmux.c.o
  __fixsfsi                          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  get_frequency_for_cpu              74      2  Code  Lc  hpm_clock_drv.c.o
  xor_check                          74      2  Code  Gb  protocol.o
  SpiSlaveSend                       72      2  Code  Gb  spi.o
  __floatunsidf                      72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  __extendsfdf2                      70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gedf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ledf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  spi_read_command                   70      2  Code  Gb  hpm_spi_drv.c.o
  Drv_FlashWrite                     68      2  Code  Gb  FirmwareUpdateFile.o
  board_init                         68      2  Code  Gb  board.c.o
  ldexpf                             68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexpf.localalias                  68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  puts                               68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  spi_master_get_default_format_config
                                     68      2  Code  Gb  hpm_spi_drv.c.o
  rom_xpi_nor_program                66      2  Code  Lc  flash.o
  spi_write_command                  66      2  Code  Gb  hpm_spi_drv.c.o
  board_init_spi_clock               64      2  Code  Gb  board.c.o
  spi_wait_for_idle_status           64      2  Code  Gb  hpm_spi_drv.c.o
  sysctl_resource_target_set_mode
                                     64      2  Code  Lc  board.c.o
  uart_flush                         64      2  Code  Gb  hpm_uart_drv.c.o
  __gesf2                            62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  clock_remove_from_group            62      2  Code  Gb  hpm_clock_drv.c.o
  spi_no_data                        62      2  Code  Lc  hpm_spi_drv.c.o
  Drv_FlashErase                     60      2  Code  Gb  FirmwareUpdateFile.o
  rom_xpi_nor_read                   60      2  Code  Lc  flash.o
  spi_slave_get_default_format_config
                                     60      2  Code  Gb  hpm_spi_drv.c.o
  uart_modem_config                  60      2  Code  Lc  hpm_uart_drv.c.o
  Check_16bit                        58      2  Code  Gb  protocol.o
  Check_8bit                         58      2  Code  Gb  protocol.o
  __ltsf2                            58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  get_frequency_for_ewdg             58      2  Code  Lc  hpm_clock_drv.c.o
  norflash_read                      58      2  Code  Gb  flash.o
  norflash_write                     58      2  Code  Gb  flash.o
  rom_xpi_nor_erase_sector           58      2  Code  Lc  flash.o
  spi_get_rx_fifo_valid_data_size
                                     58      2  Code  Lc  hpm_spi_drv.c.o
  spi_slave_get_default_control_config
                                     58      2  Code  Gb  hpm_spi_drv.c.o
  Bind_Init                          56      2  Code  Gb  navi.o
  crc_verify_8bit                    56      2  Code  Lc  SetParaBao.o
  l1c_dc_enable                      54      2  Code  Gb  hpm_l1c_drv.c.o
  clock_check_in_group               52      2  Code  Gb  hpm_clock_drv.c.o
  signal                             52      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  spi_write_address                  52      2  Code  Gb  hpm_spi_drv.c.o
  __fixunsdfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtsf2                            50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  norflash_erase_sector              50      2  Code  Gb  flash.o
  __SEGGER_RTL_print_padding
                                     48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  get_frequency_for_ahb              48      2  Code  Lc  hpm_clock_drv.c.o
  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.c.o
  printf                             46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  sysctl_clock_target_is_busy
                                     46      2  Code  Lc  hpm_sysctl_drv.c.o
  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_isctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_puts_no_nl            44      2  Code  Lc  execops.o (libc_rv32imac_balanced.a)
  __eqsf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __nesf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __trunctfsf2                       44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  enable_plic_feature                44      2  Code  Gb  system.c.o
  exception_handler                  44      2  Code  Wk  trap.c.o
  frexpf                             44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  gptmr_start_counter                44      2  Code  Lc  Timer.o
  rom_xpi_nor_get_property           44      2  Code  Lc  flash.o
  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  fputc                              42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  sysctl_clock_set_preset            42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                                     42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                                     42      2  Code  Lc  hpm_sysctl_drv.c.o
  clock_connect_group_to_cpu
                                     40      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_pewdg            40      2  Code  Lc  hpm_clock_drv.c.o
  norflash_get_chip_size             40      2  Code  Gb  flash.o
  rom_xpi_nor_auto_config            40      2  Code  Lc  flash.o
  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  pllctlv2_xtal_set_rampup_time
                                     38      2  Code  Lc  board.c.o
  sysctl_resource_target_get_mode
                                     38      2  Code  Lc  board.c.o
  __SEGGER_RTL_SquareHi_U64          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  clock_update_core_clock            36      2  Code  Gb  hpm_clock_drv.c.o
  gptmr_check_status                 36      2  Code  Lc  Timer.o
  gptmr_check_status                 36      2  Code  Lc  board.c.o
  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  board_init_uart                    34      2  Code  Gb  board.c.o
  itoa                               34      2  Code  Wk  convops.o (libc_rv32imac_balanced.a)
  spi_get_data_length_in_bytes
                                     34      2  Code  Lc  hpm_spi_drv.c.o
  sysctl_cpu_clock_any_is_busy
                                     34      2  Code  Lc  hpm_sysctl_drv.c.o
  uart_check_status                  34      2  Code  Lc  Uart_Irq.o
  Drv_FlashRead                      32      2  Code  Gb  FirmwareUpdateFile.o
  reset_handler                      32      2  Code  Wk  reset.c.o
  spi_get_data_length_in_bits
                                     32      2  Code  Lc  hpm_spi_drv.c.o
  usb_phy_disable_dp_dm_pulldown
                                     32      2  Code  Lc  board.c.o
  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  gptmr_enable_irq                   28      2  Code  Lc  Timer.o
  pllctlv2_xtal_is_enabled           28      2  Code  Lc  board.c.o
  pllctlv2_xtal_is_stable            28      2  Code  Lc  board.c.o
  sysctl_resource_any_is_busy
                                     28      2  Code  Lc  board.c.o
  uart_disable_irq                   28      2  Code  Lc  Uart_Irq.o
  uart_disable_irq                   28      2  Code  Lc  hpm_uart_drv.c.o
  Drv_SystemReset                    26      2  Code  Gb  FirmwareUpdateFile.o
  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  spi_master_get_default_timing_config
                                     26      2  Code  Gb  hpm_spi_drv.c.o
  board_delay_ms                     24      2  Code  Gb  board.c.o
  board_init_spi_pins                24      2  Code  Gb  board.c.o
  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.c.o
  uart_enable_irq                    24      2  Code  Lc  Uart_Irq.o
  uart_enable_irq                    24      2  Code  Lc  hpm_uart_drv.c.o
  uart_get_irq_id                    24      2  Code  Lc  Uart_Irq.o
  uart_write_byte                    24      2  Code  Lc  Uart_Irq.o
  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  AlgorithmDo                        20      2  Code  Gb  arithmetic.o
  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_current_locale
                                     20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  gptmr_clear_status                 20      2  Code  Lc  Timer.o
  gptmr_clear_status                 20      2  Code  Lc  board.c.o
  __SEGGER_RTL_float32_isnan
                                     18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnormal
                                     18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ppor_sw_reset                      18      2  Code  Lc  FirmwareUpdateFile.o
  syscall_handler                    18      2  Code  Wk  trap.c.o
  uart_read_byte                     18      2  Code  Lc  Uart_Irq.o
  GetZUPTFlag                        16      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_ascii_tolower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towlower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  abort                              16      2  Code  Wk  execops.o (libc_rv32imac_balanced.a)
  putchar                            16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_toupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isinf
                                     14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __subdf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subsf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                                     12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  abs                                10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  asin                               10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  cos                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  sin                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_signbit
                                      4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  _init                               4      2  Code  Wk  reset.c.o
  board_init_pmp                      4      2  Code  Gb  board.c.o
  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  swi_isr                             4      2  Code  Wk  trap.c.o
  __SEGGER_RTL_SIGNAL_SIG_DFL
                                      2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_ERR
                                      2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_SIGNAL_SIG_IGN
                                      2      2  Code  Gb  execops.o (libc_rv32imac_balanced.a)
  exit                                2      2  Code  Gb  startup.s.o
  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  start                                      2  Code  Gb  startup.s.o

Read-write data symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  Acc                        0x00089208  gp+0x0660          24      8  Zero  Lc  arithmetic.o
  ImuData                    0x0008AEE8                     28      4  Zero  Gb  main.o
  InavOutData                0x0008AE5C                     80      4  Zero  Gb  arithmetic.o
  LastAcc                    0x000891F0  gp+0x0648          24      8  Zero  Lc  arithmetic.o
  TimeStamp                  0x0008AF60                      4      4  Zero  Gb  main.o
  __RAL_global_locale        0x00080000                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_aSigTab       0x0008AF04                     24      4  Zero  Lc  execops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_global_locale
                             0x00080000                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_heap_globals  0x0008AF5C                      4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_locale_ptr    0x0008AF58                      4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stdout_file   0x0008AF54                      4      4  Zero  Lc  hpm_debug_console.c.o
  acc_sum                    0x000891D8  gp+0x0630          24      8  Zero  Lc  arithmetic.o
  bias_estimate_done         0x0008AF50                      4      4  Zero  Lc  arithmetic.o
  bias_sample_count          0x0008AF4C                      4      4  Zero  Lc  arithmetic.o
  combineData                0x0008AEAC                     32      4  Zero  Gb  arithmetic.o
  control_Slave_config       0x0008AF28                     12      4  Zero  Gb  spi.o
  control_config             0x0008AF1C                     12      4  Zero  Gb  spi.o
  estimated_acc_bias         0x000891C0  gp+0x0618          24      8  Zero  Lc  arithmetic.o
  estimated_gyro_bias        0x000891A8  gp+0x0600          24      8  Zero  Lc  arithmetic.o
  flag.2                     0x0008AF48                      4      4  Zero  Lc  SetParaBao.o
  fpga_syn                   0x0008AF6A                      1         Zero  Gb  main.o
  g_AccANNCompen             0x00087CA0                  1 800      8  Zero  Gb  main.o
  g_Align                    0x00088FD8  gp+0x0430         184      8  Zero  Gb  main.o
  g_CmdFullTempCompenData    0x00086E18                  1 920      8  Zero  Gb  main.o
  g_CmdNormalTempCompenData  0x00088E78  gp+0x02D0         352      8  Zero  Gb  main.o
  g_Compen                   0x00083B30                 10 200      8  Zero  Gb  main.o
  g_GyroANNCompen            0x00087598                  1 800      8  Zero  Gb  main.o
  g_InertialSysAlign         0x00088978  gp-0x0230         832      8  Zero  Gb  main.o
  g_InitBind                 0x00089110  gp+0x0568          80      8  Zero  Gb  main.o
  g_Kalman                   0x00080018                 15 128      8  Zero  Gb  main.o
  g_Navi                     0x00086308                  2 832      8  Zero  Gb  main.o
  g_SelfTest                 0x00089090  gp+0x04E8         128      8  Zero  Gb  main.o
  g_StartUpdateFirm          0x0008AF69                      1         Zero  Gb  SetParaBao.o
  g_SysVar                   0x00088CB8  gp+0x0110         448      8  Zero  Gb  main.o
  g_UpdateBackFlag           0x0008AF7C                      1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful         0x0008AF68                      1         Zero  Gb  SetParaBao.o
  g_ZUPT                     0x000883A8  gp-0x0800       1 488      8  Zero  Gb  main.o
  g_console_uart             0x0008AF44                      4      4  Zero  Lc  hpm_debug_console.c.o
  g_ucSystemResetFlag        0x0008AF67                      1         Zero  Gb  SetParaBao.o
  gbtxcompleted              0x0008AF78                      4      4  Init  Gb  Uart_Irq.o
  gframeParsebuf             0x0008A95C                  1 024      4  Zero  Gb  Uart_Irq.o
  grxbuffer                  0x0008AF80                  4 096      4  None  Gb  Uart_Irq.o
  grxlen                     0x0008AF40                      4      4  Zero  Gb  Uart_Irq.o
  grxst                      0x0008AF3C                      4      4  Zero  Gb  Uart_Irq.o
  gyro_sum                   0x00089190  gp+0x05E8          24      8  Zero  Lc  arithmetic.o
  hpm_core_clock             0x0008AF38                      4      4  Zero  Gb  hpm_clock_drv.c.o
  nbr_data_to_send           0x0008AF74                      4      4  Init  Gb  Uart_Irq.o
  r_Gyro                     0x00089178  gp+0x05D0          24      8  Zero  Lc  arithmetic.o
  r_LastGyro                 0x00089160  gp+0x05B8          24      8  Zero  Lc  arithmetic.o
  s_xpi_nor_config           0x0008AD5C                    256      4  Zero  Lc  flash.o
  stSetPara                  0x00089220  gp+0x0678       5 946      4  Zero  Gb  SetParaBao.o
  stSmi240Data               0x0008AECC                     28      4  Zero  Gb  protocol.o
  stdout                     0x0008AF70                      4      4  Init  Gb  hpm_debug_console.c.o
  tCnt.0                     0x0008AF64                      2      2  Zero  Lc  main.o
  timer_cb                   0x0008AF34                      4      4  Zero  Lc  board.c.o
  tx_buffer                  0x0008BF80                  4 096      4  None  Gb  Uart_Irq.o
  tx_counter                 0x0008A95A                      2      2  Zero  Gb  Uart_Irq.o
  uart_rx_dma_done           0x0008AF66                      1         Zero  Gb  uart_dma.o
  uart_tx_dma_done           0x0008AF6B                      1         Init  Gb  uart_dma.o
  uiLastBaoInDex.3           0x0008AF6C                      4      4  Init  Lc  SetParaBao.o
  uiOffsetAddr.1             0x00080014                      4      4  Zero  Lc  SetParaBao.o

Read-write data symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00080000             __SEGGER_RTL_global_locale
                                                            20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x00080000             __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x00080014             uiOffsetAddr.1                      4      4  Zero  Lc  SetParaBao.o
  0x00080018             g_Kalman                       15 128      8  Zero  Gb  main.o
  0x00083B30             g_Compen                       10 200      8  Zero  Gb  main.o
  0x00086308             g_Navi                          2 832      8  Zero  Gb  main.o
  0x00086E18             g_CmdFullTempCompenData         1 920      8  Zero  Gb  main.o
  0x00087598             g_GyroANNCompen                 1 800      8  Zero  Gb  main.o
  0x00087CA0             g_AccANNCompen                  1 800      8  Zero  Gb  main.o
  0x000883A8  gp-0x0800  g_ZUPT                          1 488      8  Zero  Gb  main.o
  0x00088978  gp-0x0230  g_InertialSysAlign                832      8  Zero  Gb  main.o
  0x00088CB8  gp+0x0110  g_SysVar                          448      8  Zero  Gb  main.o
  0x00088E78  gp+0x02D0  g_CmdNormalTempCompenData         352      8  Zero  Gb  main.o
  0x00088FD8  gp+0x0430  g_Align                           184      8  Zero  Gb  main.o
  0x00089090  gp+0x04E8  g_SelfTest                        128      8  Zero  Gb  main.o
  0x00089110  gp+0x0568  g_InitBind                         80      8  Zero  Gb  main.o
  0x00089160  gp+0x05B8  r_LastGyro                         24      8  Zero  Lc  arithmetic.o
  0x00089178  gp+0x05D0  r_Gyro                             24      8  Zero  Lc  arithmetic.o
  0x00089190  gp+0x05E8  gyro_sum                           24      8  Zero  Lc  arithmetic.o
  0x000891A8  gp+0x0600  estimated_gyro_bias                24      8  Zero  Lc  arithmetic.o
  0x000891C0  gp+0x0618  estimated_acc_bias                 24      8  Zero  Lc  arithmetic.o
  0x000891D8  gp+0x0630  acc_sum                            24      8  Zero  Lc  arithmetic.o
  0x000891F0  gp+0x0648  LastAcc                            24      8  Zero  Lc  arithmetic.o
  0x00089208  gp+0x0660  Acc                                24      8  Zero  Lc  arithmetic.o
  0x00089220  gp+0x0678  stSetPara                       5 946      4  Zero  Gb  SetParaBao.o
  0x0008A95A             tx_counter                          2      2  Zero  Gb  Uart_Irq.o
  0x0008A95C             gframeParsebuf                  1 024      4  Zero  Gb  Uart_Irq.o
  0x0008AD5C             s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  0x0008AE5C             InavOutData                        80      4  Zero  Gb  arithmetic.o
  0x0008AEAC             combineData                        32      4  Zero  Gb  arithmetic.o
  0x0008AECC             stSmi240Data                       28      4  Zero  Gb  protocol.o
  0x0008AEE8             ImuData                            28      4  Zero  Gb  main.o
  0x0008AF04             __SEGGER_RTL_aSigTab               24      4  Zero  Lc  execops.o (libc_rv32imac_balanced.a)
  0x0008AF1C             control_config                     12      4  Zero  Gb  spi.o
  0x0008AF28             control_Slave_config               12      4  Zero  Gb  spi.o
  0x0008AF34             timer_cb                            4      4  Zero  Lc  board.c.o
  0x0008AF38             hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  0x0008AF3C             grxst                               4      4  Zero  Gb  Uart_Irq.o
  0x0008AF40             grxlen                              4      4  Zero  Gb  Uart_Irq.o
  0x0008AF44             g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  0x0008AF48             flag.2                              4      4  Zero  Lc  SetParaBao.o
  0x0008AF4C             bias_sample_count                   4      4  Zero  Lc  arithmetic.o
  0x0008AF50             bias_estimate_done                  4      4  Zero  Lc  arithmetic.o
  0x0008AF54             __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  0x0008AF58             __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x0008AF5C             __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0x0008AF60             TimeStamp                           4      4  Zero  Gb  main.o
  0x0008AF64             tCnt.0                              2      2  Zero  Lc  main.o
  0x0008AF66             uart_rx_dma_done                    1         Zero  Gb  uart_dma.o
  0x0008AF67             g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  0x0008AF68             g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  0x0008AF69             g_StartUpdateFirm                   1         Zero  Gb  SetParaBao.o
  0x0008AF6A             fpga_syn                            1         Zero  Gb  main.o
  0x0008AF6B             uart_tx_dma_done                    1         Init  Gb  uart_dma.o
  0x0008AF6C             uiLastBaoInDex.3                    4      4  Init  Lc  SetParaBao.o
  0x0008AF70             stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  0x0008AF74             nbr_data_to_send                    4      4  Init  Gb  Uart_Irq.o
  0x0008AF78             gbtxcompleted                       4      4  Init  Gb  Uart_Irq.o
  0x0008AF7C             g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  0x0008AF80             grxbuffer                       4 096      4  None  Gb  Uart_Irq.o
  0x0008BF80             tx_buffer                       4 096      4  None  Gb  Uart_Irq.o

Read-write data symbols by descending size:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  g_Kalman                       15 128      8  Zero  Gb  main.o
  g_Compen                       10 200      8  Zero  Gb  main.o
  stSetPara                       5 946      4  Zero  Gb  SetParaBao.o
  grxbuffer                       4 096      4  None  Gb  Uart_Irq.o
  tx_buffer                       4 096      4  None  Gb  Uart_Irq.o
  g_Navi                          2 832      8  Zero  Gb  main.o
  g_CmdFullTempCompenData         1 920      8  Zero  Gb  main.o
  g_AccANNCompen                  1 800      8  Zero  Gb  main.o
  g_GyroANNCompen                 1 800      8  Zero  Gb  main.o
  g_ZUPT                          1 488      8  Zero  Gb  main.o
  gframeParsebuf                  1 024      4  Zero  Gb  Uart_Irq.o
  g_InertialSysAlign                832      8  Zero  Gb  main.o
  g_SysVar                          448      8  Zero  Gb  main.o
  g_CmdNormalTempCompenData         352      8  Zero  Gb  main.o
  s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  g_Align                           184      8  Zero  Gb  main.o
  g_SelfTest                        128      8  Zero  Gb  main.o
  InavOutData                        80      4  Zero  Gb  arithmetic.o
  g_InitBind                         80      8  Zero  Gb  main.o
  combineData                        32      4  Zero  Gb  arithmetic.o
  ImuData                            28      4  Zero  Gb  main.o
  stSmi240Data                       28      4  Zero  Gb  protocol.o
  Acc                                24      8  Zero  Lc  arithmetic.o
  LastAcc                            24      8  Zero  Lc  arithmetic.o
  __SEGGER_RTL_aSigTab               24      4  Zero  Lc  execops.o (libc_rv32imac_balanced.a)
  acc_sum                            24      8  Zero  Lc  arithmetic.o
  estimated_acc_bias                 24      8  Zero  Lc  arithmetic.o
  estimated_gyro_bias                24      8  Zero  Lc  arithmetic.o
  gyro_sum                           24      8  Zero  Lc  arithmetic.o
  r_Gyro                             24      8  Zero  Lc  arithmetic.o
  r_LastGyro                         24      8  Zero  Lc  arithmetic.o
  __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_global_locale
                                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  control_Slave_config               12      4  Zero  Gb  spi.o
  control_config                     12      4  Zero  Gb  spi.o
  TimeStamp                           4      4  Zero  Gb  main.o
  __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  bias_estimate_done                  4      4  Zero  Lc  arithmetic.o
  bias_sample_count                   4      4  Zero  Lc  arithmetic.o
  flag.2                              4      4  Zero  Lc  SetParaBao.o
  g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  gbtxcompleted                       4      4  Init  Gb  Uart_Irq.o
  grxlen                              4      4  Zero  Gb  Uart_Irq.o
  grxst                               4      4  Zero  Gb  Uart_Irq.o
  hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  nbr_data_to_send                    4      4  Init  Gb  Uart_Irq.o
  stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  timer_cb                            4      4  Zero  Lc  board.c.o
  uiLastBaoInDex.3                    4      4  Init  Lc  SetParaBao.o
  uiOffsetAddr.1                      4      4  Zero  Lc  SetParaBao.o
  tCnt.0                              2      2  Zero  Lc  main.o
  tx_counter                          2      2  Zero  Gb  Uart_Irq.o
  fpga_syn                            1         Zero  Gb  main.o
  g_StartUpdateFirm                   1         Zero  Gb  SetParaBao.o
  g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  uart_rx_dma_done                    1         Zero  Gb  uart_dma.o
  uart_tx_dma_done                    1         Init  Gb  uart_dma.o

Read-only data symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_Moeller_inverse_lut
                             0x8001147C                  1 024      4  Cnst  Lc  intops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_aPower2f      0x8001187C                     24      4  Cnst  Lc  utilops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_aSqrtData     0x800112FC                    384      4  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_ctype_map
                             0x80011A30                    128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_ctype_mask
                             0x80012D54                     13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale      0x800119AC                     12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_day_names
                             0x80012C88                     29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_month_names
                             0x80012BCC                     49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_am_pm_indicator
                             0x80013770                      7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_data
                             0x800119B8                     88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_date_format
                             0x80012F68                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_date_time_format
                             0x80013760                     15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_day_names
                             0x80012960                     58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_month_names
                             0x80013778                     87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_time_format
                             0x80012E8C                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_codeset_ascii
                             0x80011A10                     32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_data_empty_string
                             0x80012B84                      1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_data_utf8_period
                             0x800120E4                      2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float64_ASinACos
                             0x80010560  tp-0x0330         112      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_ATan  0x800104C8  tp-0x03C8          96      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_SinCos
                             0x800105D0  tp-0x02C0          64      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_Tan   0x80010528  tp-0x0368          56      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_hex_lc        0x80011894                     16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_hex_uc        0x800118A4                     16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ipow10        0x80010610  tp-0x0280         160      8  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_init_data__       0x800358EC                [1 736]      4  Cnst  Lc  [ Linker created ]
  __SEGGER_init_table__      0x80035898                   [84]      4  Cnst  Lc  [ Linker created ]
  fw_info                    0x8000E010                    128      4  Cnst  Gb  hpm_bootheader.c.o
  header                     0x8000E000                     16      4  Cnst  Gb  hpm_bootheader.c.o
  option                     0x8000D000                     16      4  Cnst  Gb  board.c.o
  s_adc_clk_mux_node         0x80011C28                      2      4  Cnst  Lc  hpm_clock_drv.c.o
  s_dac_clk_mux_node         0x80011D00                      2      4  Cnst  Lc  hpm_clock_drv.c.o
  s_wdgs                     0x80011174                      8      4  Cnst  Lc  hpm_clock_drv.c.o

Read-only data symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x8000D000             option                             16      4  Cnst  Gb  board.c.o
  0x8000E000             header                             16      4  Cnst  Gb  hpm_bootheader.c.o
  0x8000E010             fw_info                           128      4  Cnst  Gb  hpm_bootheader.c.o
  0x800104C8  tp-0x03C8  __SEGGER_RTL_float64_ATan          96      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010528  tp-0x0368  __SEGGER_RTL_float64_Tan           56      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010560  tp-0x0330  __SEGGER_RTL_float64_ASinACos
                                                           112      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x800105D0  tp-0x02C0  __SEGGER_RTL_float64_SinCos
                                                            64      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010610  tp-0x0280  __SEGGER_RTL_ipow10               160      8  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80011174             s_wdgs                              8      4  Cnst  Lc  hpm_clock_drv.c.o
  0x800112FC             __SEGGER_RTL_aSqrtData            384      4  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8001147C             __SEGGER_RTL_Moeller_inverse_lut
                                                         1 024      4  Cnst  Lc  intops.o (libc_rv32imac_balanced.a)
  0x8001187C             __SEGGER_RTL_aPower2f              24      4  Cnst  Lc  utilops.o (libc_rv32imac_balanced.a)
  0x80011894             __SEGGER_RTL_hex_lc                16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x800118A4             __SEGGER_RTL_hex_uc                16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x800119AC             __SEGGER_RTL_c_locale              12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800119B8             __SEGGER_RTL_c_locale_data
                                                            88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011A10             __SEGGER_RTL_codeset_ascii
                                                            32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011A30             __SEGGER_RTL_ascii_ctype_map
                                                           128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011C28             s_adc_clk_mux_node                  2      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80011D00             s_dac_clk_mux_node                  2      4  Cnst  Lc  hpm_clock_drv.c.o
  0x800120E4             __SEGGER_RTL_data_utf8_period
                                                             2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012960             __SEGGER_RTL_c_locale_day_names
                                                            58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012B84             __SEGGER_RTL_data_empty_string
                                                             1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012BCC             __SEGGER_RTL_c_locale_abbrev_month_names
                                                            49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012C88             __SEGGER_RTL_c_locale_abbrev_day_names
                                                            29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012D54             __SEGGER_RTL_ascii_ctype_mask
                                                            13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012E8C             __SEGGER_RTL_c_locale_time_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012F68             __SEGGER_RTL_c_locale_date_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80013760             __SEGGER_RTL_c_locale_date_time_format
                                                            15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80013770             __SEGGER_RTL_c_locale_am_pm_indicator
                                                             7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80013778             __SEGGER_RTL_c_locale_month_names
                                                            87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80035898             __SEGGER_init_table__            [84]      4  Cnst  Lc  [ Linker created ]
  0x800358EC             __SEGGER_init_data__          [1 736]      4  Cnst  Lc  [ Linker created ]

Untyped symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
  Symbol name                     Value     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __AHB_SRAM_segment_end__   0xF0408000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_size__  0x00008000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_start__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_end__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_start__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_end__
                             0x80010000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_size__
                             0x00002000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_end__
                             0x8000E090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_size__
                             0x00000090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __DLM_segment_end__        0x000A0000                                ----  Gb  [ Linker created ]
  __DLM_segment_size__       0x00020000                                ----  Gb  [ Linker created ]
  __DLM_segment_start__      0x00080000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_end__   0x000A0000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_size__  0x00020000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_start__
                             0x00080000                                ----  Gb  [ Linker created ]
  __HEAPSIZE__               0x00004000                                ----  Gb  [ Linker created ]
  __ILM_segment_end__        0x00020000                                ----  Gb  [ Linker created ]
  __ILM_segment_size__       0x00020000                                ----  Gb  [ Linker created ]
  __ILM_segment_start__      0x00000000                                ----  Gb  [ Linker created ]
  __ILM_segment_used_end__   0x000006A2                                ----  Gb  [ Linker created ]
  __ILM_segment_used_size__  0x000006A2                                ----  Gb  [ Linker created ]
  __ILM_segment_used_start__
                             0x00000000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_end__
                             0x8000DC00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_size__
                             0x00000C00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_end__
                             0x8000D010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_size__
                             0x00000010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __SEGGER_RTL_fdiv_reciprocal_table
                             0x800111FC                    256      4  Cnst  Lc  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __STACKSIZE__              0x00004000                                ----  Gb  [ Linker created ]
  __XPI0_segment_end__       0x80100000                                ----  Gb  [ Linker created ]
  __XPI0_segment_size__      0x000F0000                                ----  Gb  [ Linker created ]
  __XPI0_segment_start__     0x80010000                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_end__  0x80035FE4                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_size__
                             0x00025FE4                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_start__
                             0x80010000                                ----  Gb  [ Linker created ]
  __app_load_addr__          0x80010000                                ----  Gb  [ Linker created ]
  __app_offset__             0x00002000                                ----  Gb  [ Linker created ]
  __boot_header_length__     0x00000090                                ----  Gb  [ Linker created ]
  __boot_header_load_addr__  0x8000E000                                ----  Gb  [ Linker created ]
  __fsymtab_end              0x80012B85                                ----  Gb  [ Linker created ]
  __fsymtab_start            0x80012B85                                ----  Gb  [ Linker created ]
  __fw_size__                0x00001000                                ----  Gb  [ Linker created ]
  __global_pointer$          0x00088BA8  gp+0x0000                     ----  Gb  [ Linker created ]
  __heap_end__               0x00090F80                                ----  Gb  [ Linker created ]
  __heap_start__             0x0008CF80                                ----  Gb  [ Linker created ]
  __nor_cfg_option_load_addr__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __rt_init_end              0x80012B85                                ----  Gb  [ Linker created ]
  __rt_init_start            0x80012B85                                ----  Gb  [ Linker created ]
  __rtmsymtab_end            0x80012B85                                ----  Gb  [ Linker created ]
  __rtmsymtab_start          0x80012B85                                ----  Gb  [ Linker created ]
  __stack_end__              0x000A0000                                ----  Gb  [ Linker created ]
  __startup_complete         0x80010054                             2  Code  Lc  startup.s.o
  __thread_pointer$          0x80010890  tp+0x0000                     ----  Gb  [ Linker created ]
  __usbh_class_info_end__    0x00080000                                ----  Gb  [ Linker created ]
  __usbh_class_info_start__  0x00080000                                ----  Gb  [ Linker created ]
  __vector_table             0x00000000                  [292]    512  Init  Gb  startup.s.o
  __vsymtab_end              0x80012B85                                ----  Gb  [ Linker created ]
  __vsymtab_start            0x80012B85                                ----  Gb  [ Linker created ]
  _flash_size                0x00100000                                ----  Gb  [ Linker created ]
  _stack                     0x000A0000                                ----  Gb  [ Linker created ]
  _stack_safe                0x000A0000                                ----  Gb  [ Linker created ]
  default_irq_handler        0x00000562                             4  Init  Wk  startup.s.o
  default_isr_1              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_10             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_11             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_12             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_13             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_14             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_15             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_16             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_17             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_18             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_19             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_2              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_20             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_21             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_22             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_23             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_24             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_25             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_26             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_27             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_28             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_29             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_3              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_30             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_31             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_32             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_33             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_34             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_35             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_36             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_37             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_38             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_39             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_4              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_40             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_41             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_42             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_43             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_44             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_45             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_46             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_47             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_48             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_49             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_5              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_50             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_51             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_52             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_53             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_54             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_55             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_56             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_57             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_58             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_59             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_6              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_60             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_61             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_62             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_63             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_64             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_65             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_66             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_67             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_68             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_69             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_7              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_70             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_71             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_72             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_8              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_9              0x00000562                             4  Init  Wk  startup.s.o
  nmi_handler                0x00000560                    [6]      4  Init  Wk  startup.s.o

Untyped symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x80010890
  
       Value     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00000000             __vector_table                  [292]    512  Init  Gb  startup.s.o
  0x00000000             __ILM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __ILM_segment_start__                         ----  Gb  [ Linker created ]
  0x00000000             __AHB_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000010             __NOR_CFG_OPTION_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000090             __boot_header_length__                        ----  Gb  [ Linker created ]
  0x00000090             __BOOT_HEADER_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000560             nmi_handler                       [6]      4  Init  Wk  startup.s.o
  0x00000562             default_isr_9                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_8                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_72                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_71                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_70                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_7                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_69                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_68                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_67                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_66                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_65                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_64                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_63                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_62                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_61                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_60                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_6                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_59                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_58                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_57                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_56                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_55                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_54                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_53                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_52                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_51                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_50                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_5                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_49                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_48                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_47                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_46                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_45                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_44                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_43                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_42                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_41                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_40                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_4                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_39                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_38                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_37                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_36                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_35                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_34                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_33                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_32                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_31                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_30                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_3                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_29                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_28                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_27                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_26                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_25                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_24                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_23                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_22                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_21                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_20                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_2                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_19                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_18                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_17                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_16                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_15                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_14                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_13                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_12                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_11                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_10                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_1                              4  Init  Wk  startup.s.o
  0x00000562             default_irq_handler                        4  Init  Wk  startup.s.o
  0x000006A2             __ILM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x000006A2             __ILM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x00000C00             __NOR_CFG_OPTION_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00001000             __fw_size__                                   ----  Gb  [ Linker created ]
  0x00002000             __app_offset__                                ----  Gb  [ Linker created ]
  0x00002000             __BOOT_HEADER_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00004000             __STACKSIZE__                                 ----  Gb  [ Linker created ]
  0x00004000             __HEAPSIZE__                                  ----  Gb  [ Linker created ]
  0x00008000             __AHB_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x00020000             __ILM_segment_size__                          ----  Gb  [ Linker created ]
  0x00020000             __ILM_segment_end__                           ----  Gb  [ Linker created ]
  0x00020000             __DLM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x00020000             __DLM_segment_size__                          ----  Gb  [ Linker created ]
  0x00025FE4             __XPI0_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_start__                     ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_end__                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_start__                         ----  Gb  [ Linker created ]
  0x00088BA8  gp+0x0000  __global_pointer$                             ----  Gb  [ Linker created ]
  0x0008CF80             __heap_start__                                ----  Gb  [ Linker created ]
  0x00090F80             __heap_end__                                  ----  Gb  [ Linker created ]
  0x000A0000             _stack_safe                                   ----  Gb  [ Linker created ]
  0x000A0000             _stack                                        ----  Gb  [ Linker created ]
  0x000A0000             __stack_end__                                 ----  Gb  [ Linker created ]
  0x000A0000             __DLM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x000A0000             __DLM_segment_end__                           ----  Gb  [ Linker created ]
  0x000F0000             __XPI0_segment_size__                         ----  Gb  [ Linker created ]
  0x00100000             _flash_size                                   ----  Gb  [ Linker created ]
  0x8000D000             __nor_cfg_option_load_addr__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D010             __NOR_CFG_OPTION_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000DC00             __NOR_CFG_OPTION_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __boot_header_load_addr__                     ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E090             __BOOT_HEADER_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __app_load_addr__                             ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_start__                        ----  Gb  [ Linker created ]
  0x80010000             __BOOT_HEADER_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010054             __startup_complete                         2  Code  Lc  startup.s.o
  0x80010890  tp+0x0000  __thread_pointer$                             ----  Gb  [ Linker created ]
  0x800111FC             __SEGGER_RTL_fdiv_reciprocal_table
                                                           256      4  Cnst  Lc  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80012B85             __vsymtab_start                               ----  Gb  [ Linker created ]
  0x80012B85             __vsymtab_end                                 ----  Gb  [ Linker created ]
  0x80012B85             __rtmsymtab_start                             ----  Gb  [ Linker created ]
  0x80012B85             __rtmsymtab_end                               ----  Gb  [ Linker created ]
  0x80012B85             __rt_init_start                               ----  Gb  [ Linker created ]
  0x80012B85             __rt_init_end                                 ----  Gb  [ Linker created ]
  0x80012B85             __fsymtab_start                               ----  Gb  [ Linker created ]
  0x80012B85             __fsymtab_end                                 ----  Gb  [ Linker created ]
  0x80035FE4             __XPI0_segment_used_end__                     ----  Gb  [ Linker created ]
  0x80100000             __XPI0_segment_end__                          ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0408000             __AHB_SRAM_segment_end__                      ----  Gb  [ Linker created ]


***********************************************************************************************
***                                                                                         ***
***                                      LINK SUMMARY                                       ***
***                                                                                         ***
***********************************************************************************************

Memory breakdown:

  146 844 bytes read-only  code    + 
   10 615 bytes read-only  data    = 157 459 bytes read-only (total)
   85 885 bytes read-write data

Region summary:

  Name        Range                     Size                 Used               Unused       Alignment Loss
  ----------  -----------------  -----------  -------------------  -------------------  -------------------
  ILM         00000000-0001ffff      131 072        1 696   1.29%      129 374  98.70%            2   0.00%
  DLM         00080000-0009ffff      131 072       85 885  65.53%       45 187  34.47%            0   0.00%
  NOR_CFG_OPTION
              8000d000-8000dbff        3 072           16   0.52%        3 056  99.48%            0   0.00%
  BOOT_HEADER
              8000e000-8000ffff        8 192          144   1.76%        8 048  98.24%            0   0.00%
  XPI0        80010000-800fffff      983 040      155 603  15.83%      827 426  84.17%           11   0.00%

Link complete: 0 errors, 0 warnings, 0 remarks
